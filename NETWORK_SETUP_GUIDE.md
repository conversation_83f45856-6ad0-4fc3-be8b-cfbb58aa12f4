# HVNC Remote Network Setup Guide

This guide explains how to configure HVNC for remote connections across different networks, including internet connections where the client and server are on different WiFi networks or internet connections.

## Table of Contents
1. [Quick Start](#quick-start)
2. [Network Configuration Options](#network-configuration-options)
3. [Local Network Setup](#local-network-setup)
4. [Remote Network Setup (Internet)](#remote-network-setup-internet)
5. [Port Forwarding Configuration](#port-forwarding-configuration)
6. [Firewall Configuration](#firewall-configuration)
7. [Advanced Scenarios](#advanced-scenarios)
8. [Troubleshooting](#troubleshooting)

## Quick Start

### For Local Network (Same WiFi/LAN)
1. Run `configure_network.bat` and select "Quick Setup"
2. Enter the server's local IP address (e.g., ***********00)
3. Use default port 4043 or specify a custom port
4. Start the server and client

### For Remote Network (Internet)
1. Configure port forwarding on the server's router
2. Run `configure_network.bat` and select "Remote Setup"
3. Enter the server's external/public IP address
4. Configure firewall settings
5. Test the connection

## Network Configuration Options

### Configuration Methods
1. **Configuration File**: `hvnc_network.ini`
2. **Command Line Arguments**: `Client.exe --host <ip> --port <port>`
3. **Interactive Setup**: `configure_network.bat`
4. **Network Tool**: `NetworkConfigTool.exe`

### Configuration File Format
```ini
[Network]
primary_host=***********00
primary_port=4043
fallback_host=backup.server.com
fallback_port=8080
use_external_ip=false
enable_port_forwarding=false
connection_timeout=5000
retry_attempts=3
retry_delay=1000
enable_relay=false
relay_host=relay.server.com
relay_port=9090
```

## Local Network Setup

### Prerequisites
- Server and client on the same network (WiFi/LAN)
- Server's local IP address
- Available port (default: 4043)

### Step-by-Step Setup
1. **Find Server's Local IP**:
   ```cmd
   ipconfig
   ```
   Look for "IPv4 Address" (e.g., ***********00)

2. **Configure Client**:
   ```cmd
   Client.exe --host ***********00 --port 4043
   ```
   Or edit `hvnc_network.ini`:
   ```ini
   primary_host=***********00
   primary_port=4043
   ```

3. **Start Server**:
   ```cmd
   Server.exe
   ```
   Enter port: 4043

4. **Start Client**:
   ```cmd
   Client.exe
   ```

## Remote Network Setup (Internet)

### Prerequisites
- Server's external/public IP address
- Port forwarding configured on server's router
- Firewall configured to allow connections

### Step-by-Step Setup

#### 1. Find External IP Address
On the server machine:
```cmd
# Using PowerShell
(Invoke-WebRequest -Uri 'http://ipv4.icanhazip.com').Content.Trim()

# Using web browser
# Visit: https://whatismyipaddress.com/
```

#### 2. Configure Port Forwarding
Access your router's admin panel (usually http://***********):
1. Login with admin credentials
2. Find "Port Forwarding" or "Virtual Server" settings
3. Add new rule:
   - **External Port**: 4043 (or your chosen port)
   - **Internal IP**: Server's local IP (e.g., ***********00)
   - **Internal Port**: 4043
   - **Protocol**: TCP
4. Save and restart router

#### 3. Configure Windows Firewall
On the server machine:
```cmd
# Allow HVNC through Windows Firewall
netsh advfirewall firewall add rule name="HVNC Server" dir=in action=allow protocol=TCP localport=4043

# Or use Windows Defender Firewall GUI:
# Control Panel > System and Security > Windows Defender Firewall > Allow an app
```

#### 4. Configure Client
```cmd
Client.exe --host YOUR_EXTERNAL_IP --port 4043
```
Or edit `hvnc_network.ini`:
```ini
primary_host=************  # Your external IP
primary_port=4043
connection_timeout=10000   # Longer timeout for internet connections
retry_attempts=5           # More retries for unreliable connections
```

## Port Forwarding Configuration

### Common Router Interfaces

#### Linksys/Cisco
1. Access: http://***********
2. Navigate: Smart Wi-Fi Tools > Port Forwarding
3. Add rule with external/internal ports and server IP

#### Netgear
1. Access: http://***********
2. Navigate: Advanced > Port Forwarding/Port Triggering
3. Add custom service with port range and server IP

#### TP-Link
1. Access: http://***********
2. Navigate: Advanced > NAT Forwarding > Port Forwarding
3. Add rule with service port and internal IP

#### ASUS
1. Access: http://***********
2. Navigate: WAN > Port Forwarding
3. Enable port forwarding and add rule

### UPnP Alternative
If your router supports UPnP, enable it in the configuration:
```ini
enable_port_forwarding=true
```
This will attempt automatic port forwarding (experimental feature).

## Firewall Configuration

### Windows Defender Firewall
```cmd
# Allow specific port
netsh advfirewall firewall add rule name="HVNC" dir=in action=allow protocol=TCP localport=4043

# Allow program
netsh advfirewall firewall add rule name="HVNC Server" dir=in action=allow program="C:\path\to\Server.exe"
```

### Third-Party Firewalls
- **Norton**: Add program to allowed list
- **McAfee**: Configure firewall exceptions
- **Kaspersky**: Add application to trusted zone

### Router Firewall
Most routers have built-in firewalls. Ensure:
1. Port forwarding rules are active
2. SPI (Stateful Packet Inspection) allows established connections
3. DoS protection doesn't block legitimate traffic

## Advanced Scenarios

### Multiple Fallback Servers
```ini
primary_host=server1.example.com
primary_port=4043
fallback_host=server2.example.com
fallback_port=8080
```

### Dynamic DNS
For changing IP addresses, use dynamic DNS services:
```ini
primary_host=myhvnc.ddns.net
primary_port=4043
```

### VPN Connections
For enhanced security:
1. Set up VPN server on the target network
2. Connect client through VPN
3. Use local IP addresses within VPN

### Relay Server
For complex NAT scenarios:
1. Deploy relay server on public cloud (AWS, Azure, etc.)
2. Configure both client and server to connect to relay
3. Enable relay in configuration:
```ini
enable_relay=true
relay_host=relay.example.com
relay_port=9090
```

## Troubleshooting

### Connection Failed
1. **Check IP Address**: Verify external IP hasn't changed
2. **Test Port**: Use `telnet <ip> <port>` to test connectivity
3. **Check Firewall**: Temporarily disable to test
4. **Verify Port Forwarding**: Use online port checker tools

### Slow Performance
1. **Increase Timeout**: Set higher connection_timeout value
2. **Optimize Network**: Use wired connection when possible
3. **Check Bandwidth**: Ensure sufficient upload/download speed
4. **Reduce Quality**: Lower frame rate or compression in performance settings

### Intermittent Disconnections
1. **Enable Retry Logic**: Increase retry_attempts and retry_delay
2. **Use Fallback Server**: Configure secondary server
3. **Check Network Stability**: Monitor for packet loss
4. **Router Settings**: Disable power saving on network adapters

### Security Considerations
1. **Use Strong Passwords**: If authentication is implemented
2. **VPN Recommended**: For sensitive connections
3. **Limit IP Ranges**: Configure router to allow specific IPs only
4. **Monitor Logs**: Check for unauthorized access attempts
5. **Non-Standard Ports**: Use ports other than 4043 to reduce automated attacks

### Testing Tools
- **Network Config Tool**: `NetworkConfigTool.exe`
- **Connection Test**: `configure_network.bat` > Test Configuration
- **Port Scanner**: Use nmap or online port checkers
- **Bandwidth Test**: Use speedtest.net or similar services

### Common Error Messages
- **"Connection timeout"**: Check firewall and port forwarding
- **"Host unreachable"**: Verify IP address and internet connectivity
- **"Connection refused"**: Server not running or port blocked
- **"DNS resolution failed"**: Check hostname spelling or use IP address

## Support and Resources

### Log Files
- Client logs: `hvnc_client.log`
- Server logs: Console output
- Network tool logs: Built-in diagnostics

### Configuration Validation
Use the network configuration tool to validate settings:
```cmd
NetworkConfigTool.exe
```

### Online Resources
- Router manual for port forwarding instructions
- Dynamic DNS providers (No-IP, DynDNS, etc.)
- VPN services for secure connections
- Cloud providers for relay server hosting

For additional support, check the main README.md and USAGE.txt files.
