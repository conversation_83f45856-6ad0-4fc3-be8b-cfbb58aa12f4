#include <iostream>
#include <thread>
#include <vector>
#include <map>
#include <mutex>
#include <WinSock2.h>
#include <WS2tcpip.h>

#pragma comment(lib, "ws2_32.lib")

class HVNCRelayServer {
private:
    struct ClientConnection {
        SOCKET socket;
        std::string identifier;
        bool isServer;
        std::thread handlerThread;
        
        ClientConnection() : socket(INVALID_SOCKET), isServer(false) {}
    };
    
    std::map<std::string, ClientConnection*> m_servers;
    std::map<std::string, std::vector<ClientConnection*>> m_clients;
    std::mutex m_connectionsMutex;
    bool m_running;
    int m_port;
    
public:
    HVNCRelayServer(int port) : m_port(port), m_running(false) {}
    
    ~HVNCRelayServer() {
        Stop();
    }
    
    bool Start() {
        WSADATA wsaData;
        if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
            std::cerr << "WSAStartup failed" << std::endl;
            return false;
        }
        
        SOCKET listenSocket = socket(AF_INET, SOCK_STREAM, 0);
        if (listenSocket == INVALID_SOCKET) {
            std::cerr << "Socket creation failed" << std::endl;
            WSACleanup();
            return false;
        }
        
        // Allow socket reuse
        int opt = 1;
        setsockopt(listenSocket, SOL_SOCKET, SO_REUSEADDR, (char*)&opt, sizeof(opt));
        
        sockaddr_in serverAddr;
        serverAddr.sin_family = AF_INET;
        serverAddr.sin_addr.s_addr = INADDR_ANY;
        serverAddr.sin_port = htons(m_port);
        
        if (bind(listenSocket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
            std::cerr << "Bind failed on port " << m_port << std::endl;
            closesocket(listenSocket);
            WSACleanup();
            return false;
        }
        
        if (listen(listenSocket, SOMAXCONN) == SOCKET_ERROR) {
            std::cerr << "Listen failed" << std::endl;
            closesocket(listenSocket);
            WSACleanup();
            return false;
        }
        
        m_running = true;
        std::cout << "HVNC Relay Server started on port " << m_port << std::endl;
        std::cout << "Waiting for connections..." << std::endl;
        
        while (m_running) {
            sockaddr_in clientAddr;
            int clientAddrSize = sizeof(clientAddr);
            SOCKET clientSocket = accept(listenSocket, (sockaddr*)&clientAddr, &clientAddrSize);
            
            if (clientSocket == INVALID_SOCKET) {
                if (m_running) {
                    std::cerr << "Accept failed" << std::endl;
                }
                continue;
            }
            
            char clientIP[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &clientAddr.sin_addr, clientIP, INET_ADDRSTRLEN);
            std::cout << "New connection from " << clientIP << ":" << ntohs(clientAddr.sin_port) << std::endl;
            
            // Handle the connection in a separate thread
            std::thread([this, clientSocket, clientIP]() {
                HandleConnection(clientSocket, clientIP);
            }).detach();
        }
        
        closesocket(listenSocket);
        WSACleanup();
        return true;
    }
    
    void Stop() {
        m_running = false;
        
        std::lock_guard<std::mutex> lock(m_connectionsMutex);
        
        // Close all server connections
        for (auto& pair : m_servers) {
            if (pair.second->socket != INVALID_SOCKET) {
                closesocket(pair.second->socket);
            }
            if (pair.second->handlerThread.joinable()) {
                pair.second->handlerThread.join();
            }
            delete pair.second;
        }
        m_servers.clear();
        
        // Close all client connections
        for (auto& pair : m_clients) {
            for (auto* client : pair.second) {
                if (client->socket != INVALID_SOCKET) {
                    closesocket(client->socket);
                }
                if (client->handlerThread.joinable()) {
                    client->handlerThread.join();
                }
                delete client;
            }
        }
        m_clients.clear();
    }
    
private:
    void HandleConnection(SOCKET clientSocket, const std::string& clientIP) {
        // Simple protocol: first message indicates if this is a server or client
        // Format: "SERVER:identifier" or "CLIENT:identifier"
        
        char buffer[256];
        int bytesReceived = recv(clientSocket, buffer, sizeof(buffer) - 1, 0);
        if (bytesReceived <= 0) {
            std::cerr << "Failed to receive identification from " << clientIP << std::endl;
            closesocket(clientSocket);
            return;
        }
        
        buffer[bytesReceived] = '\0';
        std::string identification(buffer);
        
        size_t colonPos = identification.find(':');
        if (colonPos == std::string::npos) {
            std::cerr << "Invalid identification format from " << clientIP << std::endl;
            closesocket(clientSocket);
            return;
        }
        
        std::string type = identification.substr(0, colonPos);
        std::string identifier = identification.substr(colonPos + 1);
        
        std::lock_guard<std::mutex> lock(m_connectionsMutex);
        
        if (type == "SERVER") {
            HandleServerConnection(clientSocket, identifier, clientIP);
        } else if (type == "CLIENT") {
            HandleClientConnection(clientSocket, identifier, clientIP);
        } else {
            std::cerr << "Unknown connection type: " << type << " from " << clientIP << std::endl;
            closesocket(clientSocket);
        }
    }
    
    void HandleServerConnection(SOCKET serverSocket, const std::string& identifier, const std::string& clientIP) {
        std::cout << "Server registered: " << identifier << " from " << clientIP << std::endl;
        
        ClientConnection* server = new ClientConnection();
        server->socket = serverSocket;
        server->identifier = identifier;
        server->isServer = true;
        
        m_servers[identifier] = server;
        
        // Send acknowledgment
        const char* ack = "SERVER_REGISTERED";
        send(serverSocket, ack, strlen(ack), 0);
        
        // Keep the server connection alive and handle any control messages
        char buffer[1024];
        while (m_running) {
            int bytesReceived = recv(serverSocket, buffer, sizeof(buffer), 0);
            if (bytesReceived <= 0) {
                break; // Connection closed
            }
            // Handle server control messages if needed
        }
        
        // Cleanup when server disconnects
        std::cout << "Server disconnected: " << identifier << std::endl;
        m_servers.erase(identifier);
        delete server;
    }
    
    void HandleClientConnection(SOCKET clientSocket, const std::string& identifier, const std::string& clientIP) {
        std::cout << "Client connecting to server: " << identifier << " from " << clientIP << std::endl;
        
        // Check if the requested server is available
        auto serverIt = m_servers.find(identifier);
        if (serverIt == m_servers.end()) {
            std::cerr << "Server not found: " << identifier << std::endl;
            const char* error = "SERVER_NOT_FOUND";
            send(clientSocket, error, strlen(error), 0);
            closesocket(clientSocket);
            return;
        }
        
        ClientConnection* client = new ClientConnection();
        client->socket = clientSocket;
        client->identifier = identifier;
        client->isServer = false;
        
        m_clients[identifier].push_back(client);
        
        // Send acknowledgment
        const char* ack = "CLIENT_CONNECTED";
        send(clientSocket, ack, strlen(ack), 0);
        
        // Start relaying data between client and server
        SOCKET serverSocket = serverIt->second->socket;
        
        // Create threads for bidirectional data relay
        std::thread clientToServer([this, clientSocket, serverSocket, identifier, clientIP]() {
            RelayData(clientSocket, serverSocket, "Client->Server (" + identifier + ")");
        });
        
        std::thread serverToClient([this, serverSocket, clientSocket, identifier, clientIP]() {
            RelayData(serverSocket, clientSocket, "Server->Client (" + identifier + ")");
        });
        
        // Wait for either thread to finish (connection closed)
        clientToServer.join();
        serverToClient.join();
        
        // Cleanup
        std::cout << "Client disconnected from server: " << identifier << std::endl;
        auto& clientList = m_clients[identifier];
        clientList.erase(std::remove(clientList.begin(), clientList.end(), client), clientList.end());
        delete client;
    }
    
    void RelayData(SOCKET fromSocket, SOCKET toSocket, const std::string& direction) {
        char buffer[4096];
        while (m_running) {
            int bytesReceived = recv(fromSocket, buffer, sizeof(buffer), 0);
            if (bytesReceived <= 0) {
                break; // Connection closed or error
            }
            
            int bytesSent = send(toSocket, buffer, bytesReceived, 0);
            if (bytesSent <= 0) {
                break; // Connection closed or error
            }
            
            // Optional: Log data transfer for debugging
            // std::cout << direction << ": " << bytesReceived << " bytes" << std::endl;
        }
    }
};

int main(int argc, char* argv[]) {
    int port = 9090; // Default relay port
    
    if (argc > 1) {
        port = atoi(argv[1]);
        if (port <= 0 || port > 65535) {
            std::cerr << "Invalid port number: " << argv[1] << std::endl;
            return 1;
        }
    }
    
    std::cout << "HVNC Relay Server v1.0" << std::endl;
    std::cout << "======================" << std::endl;
    std::cout << "This relay server helps HVNC connections traverse NAT and firewalls." << std::endl;
    std::cout << "Usage: RelayServer.exe [port]" << std::endl;
    std::cout << "Default port: 9090" << std::endl << std::endl;
    
    HVNCRelayServer relay(port);
    
    if (!relay.Start()) {
        std::cerr << "Failed to start relay server" << std::endl;
        return 1;
    }
    
    return 0;
}
