@echo off
setlocal enabledelayedexpansion

title HVNC Network Configuration Tool
color 0A

echo ===============================================
echo    HVNC Network Configuration Tool v1.0
echo ===============================================
echo.

:main_menu
echo Main Menu:
echo 1. Quick Setup (Local Network)
echo 2. Remote Setup (Internet Connection)
echo 3. Advanced Configuration
echo 4. Test Current Configuration
echo 5. Show Network Information
echo 6. Reset to Default
echo 7. Help
echo 8. Exit
echo.
set /p choice="Enter your choice (1-8): "

if "%choice%"=="1" goto quick_setup
if "%choice%"=="2" goto remote_setup
if "%choice%"=="3" goto advanced_setup
if "%choice%"=="4" goto test_config
if "%choice%"=="5" goto show_network_info
if "%choice%"=="6" goto reset_config
if "%choice%"=="7" goto show_help
if "%choice%"=="8" goto exit
echo Invalid choice. Please try again.
goto main_menu

:quick_setup
echo.
echo === Quick Setup (Local Network) ===
echo This will configure HVNC for use on the same local network.
echo.
set /p server_ip="Enter server IP address (e.g., ***********00): "
set /p server_port="Enter server port [4043]: "
if "%server_port%"=="" set server_port=4043

echo Creating configuration...
(
echo # HVNC Network Configuration - Quick Setup
echo # Generated automatically for local network use
echo.
echo [Network]
echo primary_host=%server_ip%
echo primary_port=%server_port%
echo fallback_host=
echo fallback_port=8080
echo use_external_ip=false
echo enable_port_forwarding=false
echo connection_timeout=5000
echo retry_attempts=3
echo retry_delay=1000
echo enable_relay=false
echo relay_host=
echo relay_port=9090
) > hvnc_network.ini

echo ✓ Configuration created successfully!
echo ✓ Client will connect to %server_ip%:%server_port%
echo.
pause
goto main_menu

:remote_setup
echo.
echo === Remote Setup (Internet Connection) ===
echo This will configure HVNC for connections across the internet.
echo.
echo IMPORTANT: Before proceeding, ensure:
echo • Port forwarding is configured on the server's router
echo • Windows Firewall allows the connection
echo • You have the server's external/public IP address
echo.
pause

set /p external_ip="Enter server's external IP address: "
set /p external_port="Enter external port [4043]: "
if "%external_port%"=="" set external_port=4043

set /p use_fallback="Configure fallback server? (y/n) [n]: "
if /i "%use_fallback%"=="y" (
    set /p fallback_ip="Enter fallback IP address: "
    set /p fallback_port="Enter fallback port [8080]: "
    if "!fallback_port!"=="" set fallback_port=8080
) else (
    set fallback_ip=
    set fallback_port=8080
)

echo Creating remote configuration...
(
echo # HVNC Network Configuration - Remote Setup
echo # Generated automatically for internet connections
echo # SECURITY: Consider using VPN for sensitive connections
echo.
echo [Network]
echo primary_host=%external_ip%
echo primary_port=%external_port%
echo fallback_host=%fallback_ip%
echo fallback_port=%fallback_port%
echo use_external_ip=false
echo enable_port_forwarding=false
echo connection_timeout=10000
echo retry_attempts=5
echo retry_delay=2000
echo enable_relay=false
echo relay_host=
echo relay_port=9090
) > hvnc_network.ini

echo ✓ Remote configuration created successfully!
echo ✓ Client will connect to %external_ip%:%external_port%
if not "%fallback_ip%"=="" echo ✓ Fallback server: %fallback_ip%:%fallback_port%
echo.
echo NEXT STEPS:
echo 1. Ensure port %external_port% is forwarded to the server
echo 2. Test the connection using option 4
echo 3. Check firewall settings if connection fails
echo.
pause
goto main_menu

:advanced_setup
echo.
echo === Advanced Configuration ===
echo.
set /p primary_host="Primary host/IP: "
set /p primary_port="Primary port [4043]: "
if "%primary_port%"=="" set primary_port=4043

set /p fallback_host="Fallback host/IP (optional): "
set /p fallback_port="Fallback port [8080]: "
if "%fallback_port%"=="" set fallback_port=8080

set /p use_external="Use external IP detection? (y/n) [n]: "
if /i "%use_external%"=="y" (set use_external_ip=true) else (set use_external_ip=false)

set /p enable_upnp="Enable UPnP port forwarding? (y/n) [n]: "
if /i "%enable_upnp%"=="y" (set enable_port_forwarding=true) else (set enable_port_forwarding=false)

set /p timeout="Connection timeout in ms [5000]: "
if "%timeout%"=="" set timeout=5000

set /p retries="Retry attempts [3]: "
if "%retries%"=="" set retries=3

set /p retry_delay="Retry delay in ms [1000]: "
if "%retry_delay%"=="" set retry_delay=1000

echo Creating advanced configuration...
(
echo # HVNC Network Configuration - Advanced Setup
echo # Custom configuration with advanced options
echo.
echo [Network]
echo primary_host=%primary_host%
echo primary_port=%primary_port%
echo fallback_host=%fallback_host%
echo fallback_port=%fallback_port%
echo use_external_ip=%use_external_ip%
echo enable_port_forwarding=%enable_port_forwarding%
echo connection_timeout=%timeout%
echo retry_attempts=%retries%
echo retry_delay=%retry_delay%
echo enable_relay=false
echo relay_host=
echo relay_port=9090
) > hvnc_network.ini

echo ✓ Advanced configuration created successfully!
echo.
pause
goto main_menu

:test_config
echo.
echo === Testing Current Configuration ===
if not exist "hvnc_network.ini" (
    echo ✗ No configuration file found!
    echo Please create a configuration first.
    pause
    goto main_menu
)

echo Reading configuration...
for /f "tokens=1,2 delims==" %%a in ('type hvnc_network.ini ^| findstr "primary_host="') do set test_host=%%b
for /f "tokens=1,2 delims==" %%a in ('type hvnc_network.ini ^| findstr "primary_port="') do set test_port=%%b

echo Testing connection to %test_host%:%test_port%...
echo.

:: Simple connection test using telnet (if available)
echo Testing... (this may take a few seconds)
telnet %test_host% %test_port% 2>nul
if %errorlevel%==0 (
    echo ✓ Connection test successful!
) else (
    echo ✗ Connection test failed!
    echo.
    echo Possible issues:
    echo • Server is not running
    echo • Port forwarding not configured
    echo • Firewall blocking connection
    echo • Incorrect IP address or port
)
echo.
pause
goto main_menu

:show_network_info
echo.
echo === Network Information ===
echo.
echo Local IP Configuration:
ipconfig | findstr "IPv4"
echo.
echo External IP (if available):
:: Try to get external IP using PowerShell
powershell -Command "(Invoke-WebRequest -Uri 'http://ipv4.icanhazip.com' -UseBasicParsing).Content.Trim()" 2>nul
echo.
echo Network Connectivity:
ping -n 1 ******* >nul 2>&1
if %errorlevel%==0 (
    echo ✓ Internet connection available
) else (
    echo ✗ No internet connection
)
echo.
pause
goto main_menu

:reset_config
echo.
echo === Reset to Default Configuration ===
echo This will create a default configuration file.
echo.
set /p confirm="Are you sure? (y/n): "
if /i not "%confirm%"=="y" goto main_menu

echo Creating default configuration...
(
echo # HVNC Network Configuration - Default
echo # This configuration uses localhost for local testing
echo.
echo [Network]
echo primary_host=127.0.0.1
echo primary_port=4043
echo fallback_host=
echo fallback_port=8080
echo use_external_ip=false
echo enable_port_forwarding=false
echo connection_timeout=5000
echo retry_attempts=3
echo retry_delay=1000
echo enable_relay=false
echo relay_host=
echo relay_port=9090
) > hvnc_network.ini

echo ✓ Default configuration created successfully!
echo.
pause
goto main_menu

:show_help
echo.
echo === Help Information ===
echo.
echo This tool helps configure HVNC for different network scenarios:
echo.
echo QUICK SETUP: For connections within the same local network
echo • Use when client and server are on the same WiFi/LAN
echo • Requires only the server's local IP address
echo.
echo REMOTE SETUP: For connections across the internet
echo • Use when client and server are on different networks
echo • Requires port forwarding configuration on server's router
echo • Needs the server's external/public IP address
echo.
echo ADVANCED SETUP: For custom configurations
echo • Allows fine-tuning of all connection parameters
echo • Includes fallback servers and retry settings
echo.
echo PORT FORWARDING SETUP:
echo 1. Access your router's admin panel (usually ***********)
echo 2. Find "Port Forwarding" or "Virtual Server" settings
echo 3. Forward external port to server's local IP and port
echo 4. Save settings and restart router if needed
echo.
echo FIREWALL CONFIGURATION:
echo • Windows: Allow HVNC through Windows Defender Firewall
echo • Router: Ensure port is not blocked by router firewall
echo.
pause
goto main_menu

:exit
echo.
echo Thank you for using HVNC Network Configuration Tool!
echo.
pause
exit /b 0
