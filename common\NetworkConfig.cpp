#include "NetworkConfig.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <WinSock2.h>
#include <WS2tcpip.h>

#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "wininet.lib")

namespace ModernHVNC {
    namespace Network {

        // NetworkUtils implementation
        void NetworkUtils::SetDefaultConfig(ConnectionConfig* config) {
            if (!config) return;
            
            strcpy_s(config->primaryHost, sizeof(config->primaryHost), "127.0.0.1");
            strcpy_s(config->fallbackHost, sizeof(config->fallbackHost), "");
            config->primaryPort = 4043;
            config->fallbackPort = 8080;
            config->useExternalIP = FALSE;
            config->enablePortForwarding = FALSE;
            config->connectionTimeout = 5000;
            config->retryAttempts = 3;
            config->retryDelay = 1000;
            config->enableRelay = FALSE;
            strcpy_s(config->relayHost, sizeof(config->relayHost), "");
            config->relayPort = 9090;
        }

        BOOL NetworkUtils::LoadNetworkConfig(const char* configFile, ConnectionConfig* config) {
            if (!configFile || !config) return FALSE;
            
            FILE* file = nullptr;
            if (fopen_s(&file, configFile, "r") != 0 || !file) {
                // If config file doesn't exist, create default one
                SetDefaultConfig(config);
                SaveNetworkConfig(configFile, config);
                return TRUE;
            }
            
            SetDefaultConfig(config); // Start with defaults
            
            char line[512];
            while (fgets(line, sizeof(line), file)) {
                // Remove newline
                line[strcspn(line, "\r\n")] = 0;
                
                // Skip comments and empty lines
                if (line[0] == '#' || line[0] == ';' || line[0] == '\0') continue;
                if (strstr(line, "[Network]")) continue;
                
                char* equals = strchr(line, '=');
                if (!equals) continue;
                
                *equals = '\0';
                char* key = line;
                char* value = equals + 1;
                
                // Trim whitespace
                while (*key == ' ' || *key == '\t') key++;
                while (*value == ' ' || *value == '\t') value++;
                
                if (strcmp(key, "primary_host") == 0) {
                    strcpy_s(config->primaryHost, sizeof(config->primaryHost), value);
                } else if (strcmp(key, "primary_port") == 0) {
                    config->primaryPort = atoi(value);
                } else if (strcmp(key, "fallback_host") == 0) {
                    strcpy_s(config->fallbackHost, sizeof(config->fallbackHost), value);
                } else if (strcmp(key, "fallback_port") == 0) {
                    config->fallbackPort = atoi(value);
                } else if (strcmp(key, "use_external_ip") == 0) {
                    config->useExternalIP = (strcmp(value, "true") == 0);
                } else if (strcmp(key, "enable_port_forwarding") == 0) {
                    config->enablePortForwarding = (strcmp(value, "true") == 0);
                } else if (strcmp(key, "connection_timeout") == 0) {
                    config->connectionTimeout = atoi(value);
                } else if (strcmp(key, "retry_attempts") == 0) {
                    config->retryAttempts = atoi(value);
                } else if (strcmp(key, "retry_delay") == 0) {
                    config->retryDelay = atoi(value);
                } else if (strcmp(key, "enable_relay") == 0) {
                    config->enableRelay = (strcmp(value, "true") == 0);
                } else if (strcmp(key, "relay_host") == 0) {
                    strcpy_s(config->relayHost, sizeof(config->relayHost), value);
                } else if (strcmp(key, "relay_port") == 0) {
                    config->relayPort = atoi(value);
                }
            }
            
            fclose(file);
            return TRUE;
        }

        BOOL NetworkUtils::SaveNetworkConfig(const char* configFile, const ConnectionConfig* config) {
            if (!configFile || !config) return FALSE;
            
            FILE* file = nullptr;
            if (fopen_s(&file, configFile, "w") != 0 || !file) return FALSE;
            
            fprintf(file, "# HVNC Network Configuration\n");
            fprintf(file, "# This file configures network connection settings for remote HVNC access\n\n");
            fprintf(file, "[Network]\n");
            fprintf(file, "primary_host=%s\n", config->primaryHost);
            fprintf(file, "primary_port=%d\n", config->primaryPort);
            fprintf(file, "fallback_host=%s\n", config->fallbackHost);
            fprintf(file, "fallback_port=%d\n", config->fallbackPort);
            fprintf(file, "use_external_ip=%s\n", config->useExternalIP ? "true" : "false");
            fprintf(file, "enable_port_forwarding=%s\n", config->enablePortForwarding ? "true" : "false");
            fprintf(file, "connection_timeout=%lu\n", config->connectionTimeout);
            fprintf(file, "retry_attempts=%lu\n", config->retryAttempts);
            fprintf(file, "retry_delay=%lu\n", config->retryDelay);
            fprintf(file, "enable_relay=%s\n", config->enableRelay ? "true" : "false");
            fprintf(file, "relay_host=%s\n", config->relayHost);
            fprintf(file, "relay_port=%d\n", config->relayPort);
            
            fclose(file);
            return TRUE;
        }

        BOOL NetworkUtils::GetExternalIP(char* ipBuffer, size_t bufferSize) {
            if (!ipBuffer || bufferSize < 16) return FALSE;
            
            // Try multiple external IP services
            const char* services[] = {
                "http://checkip.amazonaws.com/",
                "http://ipv4.icanhazip.com/",
                "http://api.ipify.org/",
                "http://ipinfo.io/ip"
            };
            
            for (int i = 0; i < sizeof(services) / sizeof(services[0]); i++) {
                if (QueryExternalIPService(services[i], ipBuffer, bufferSize)) {
                    return TRUE;
                }
            }
            
            return FALSE;
        }

        BOOL NetworkUtils::QueryExternalIPService(const char* serviceUrl, char* ipBuffer, size_t bufferSize) {
            HINTERNET hInternet = InternetOpenA("HVNC-Client", INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, 0);
            if (!hInternet) return FALSE;
            
            HINTERNET hUrl = InternetOpenUrlA(hInternet, serviceUrl, NULL, 0, INTERNET_FLAG_RELOAD, 0);
            if (!hUrl) {
                InternetCloseHandle(hInternet);
                return FALSE;
            }
            
            char response[256] = {0};
            DWORD bytesRead = 0;
            BOOL result = InternetReadFile(hUrl, response, sizeof(response) - 1, &bytesRead);
            
            InternetCloseHandle(hUrl);
            InternetCloseHandle(hInternet);
            
            if (result && bytesRead > 0) {
                response[bytesRead] = '\0';
                return ParseIPFromResponse(response, ipBuffer, bufferSize);
            }
            
            return FALSE;
        }

        BOOL NetworkUtils::ParseIPFromResponse(const char* response, char* ipBuffer, size_t bufferSize) {
            if (!response || !ipBuffer) return FALSE;
            
            // Find IP address pattern in response
            const char* start = response;
            while (*start && (*start < '0' || *start > '9')) start++;
            
            if (!*start) return FALSE;
            
            const char* end = start;
            int dots = 0;
            while (*end && ((*end >= '0' && *end <= '9') || *end == '.')) {
                if (*end == '.') dots++;
                end++;
            }
            
            if (dots != 3) return FALSE;
            
            size_t ipLen = end - start;
            if (ipLen >= bufferSize) return FALSE;
            
            strncpy_s(ipBuffer, bufferSize, start, ipLen);
            ipBuffer[ipLen] = '\0';
            
            return IsValidIP(ipBuffer);
        }

        BOOL NetworkUtils::GetLocalIP(char* ipBuffer, size_t bufferSize) {
            if (!ipBuffer || bufferSize < 16) return FALSE;

            WSADATA wsaData;
            if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) return FALSE;

            char hostname[256];
            if (gethostname(hostname, sizeof(hostname)) == SOCKET_ERROR) {
                WSACleanup();
                return FALSE;
            }

            struct hostent* he = gethostbyname(hostname);
            if (!he || !he->h_addr_list[0]) {
                WSACleanup();
                return FALSE;
            }

            struct in_addr addr;
            memcpy(&addr, he->h_addr_list[0], sizeof(struct in_addr));
            strcpy_s(ipBuffer, bufferSize, inet_ntoa(addr));

            WSACleanup();
            return TRUE;
        }

        BOOL NetworkUtils::IsValidIP(const char* ip) {
            if (!ip) return FALSE;

            struct sockaddr_in sa;
            return inet_pton(AF_INET, ip, &(sa.sin_addr)) == 1;
        }

        BOOL NetworkUtils::IsPrivateIP(const char* ip) {
            if (!ip || !IsValidIP(ip)) return FALSE;

            struct sockaddr_in sa;
            inet_pton(AF_INET, ip, &(sa.sin_addr));

            unsigned long addr = ntohl(sa.sin_addr.s_addr);

            // Check for private IP ranges:
            // 10.0.0.0/8 (10.0.0.0 - **************)
            if ((addr & 0xFF000000) == 0x0A000000) return TRUE;

            // **********/12 (********** - **************)
            if ((addr & 0xFFF00000) == 0xAC100000) return TRUE;

            // ***********/16 (*********** - ***************)
            if ((addr & 0xFFFF0000) == 0xC0A80000) return TRUE;

            // *********/8 (localhost)
            if ((addr & 0xFF000000) == 0x7F000000) return TRUE;

            return FALSE;
        }

        BOOL NetworkUtils::ResolveHostname(const char* hostname, char* ipBuffer, size_t bufferSize) {
            if (!hostname || !ipBuffer || bufferSize < 16) return FALSE;

            WSADATA wsaData;
            if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) return FALSE;

            struct hostent* he = gethostbyname(hostname);
            if (!he || !he->h_addr_list[0]) {
                WSACleanup();
                return FALSE;
            }

            struct in_addr addr;
            memcpy(&addr, he->h_addr_list[0], sizeof(struct in_addr));
            strcpy_s(ipBuffer, bufferSize, inet_ntoa(addr));

            WSACleanup();
            return TRUE;
        }

        BOOL NetworkUtils::GetHostnameFromIP(const char* ip, char* hostnameBuffer, size_t bufferSize) {
            if (!ip || !hostnameBuffer || bufferSize < 256) return FALSE;

            WSADATA wsaData;
            if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) return FALSE;

            struct sockaddr_in sa;
            sa.sin_family = AF_INET;
            if (inet_pton(AF_INET, ip, &(sa.sin_addr)) != 1) {
                WSACleanup();
                return FALSE;
            }

            struct hostent* he = gethostbyaddr((char*)&sa.sin_addr, sizeof(sa.sin_addr), AF_INET);
            if (!he || !he->h_name) {
                WSACleanup();
                return FALSE;
            }

            strcpy_s(hostnameBuffer, bufferSize, he->h_name);

            WSACleanup();
            return TRUE;
        }

        BOOL NetworkUtils::IsConnectedToInternet() {
            DWORD flags;
            return InternetGetConnectedState(&flags, 0) != 0;
        }

        BOOL NetworkUtils::GetActiveNetworkInterface(char* interfaceName, size_t nameSize) {
            if (!interfaceName || nameSize < 256) return FALSE;

            // This is a simplified implementation
            // In a full implementation, you would enumerate network adapters
            strcpy_s(interfaceName, nameSize, "Default");
            return TRUE;
        }

        BOOL NetworkUtils::TestConnection(const char* host, int port, DWORD timeout) {
            if (!host || port <= 0) return FALSE;
            
            WSADATA wsaData;
            if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) return FALSE;
            
            SOCKET sock = socket(AF_INET, SOCK_STREAM, 0);
            if (sock == INVALID_SOCKET) {
                WSACleanup();
                return FALSE;
            }
            
            // Set socket to non-blocking mode for timeout
            u_long mode = 1;
            ioctlsocket(sock, FIONBIO, &mode);
            
            struct sockaddr_in addr;
            addr.sin_family = AF_INET;
            addr.sin_port = htons(port);
            
            if (inet_pton(AF_INET, host, &addr.sin_addr) != 1) {
                // Try to resolve hostname
                struct hostent* he = gethostbyname(host);
                if (!he) {
                    closesocket(sock);
                    WSACleanup();
                    return FALSE;
                }
                memcpy(&addr.sin_addr, he->h_addr_list[0], he->h_length);
            }
            
            BOOL result = FALSE;
            if (connect(sock, (struct sockaddr*)&addr, sizeof(addr)) == SOCKET_ERROR) {
                if (WSAGetLastError() == WSAEWOULDBLOCK) {
                    fd_set writeSet;
                    FD_ZERO(&writeSet);
                    FD_SET(sock, &writeSet);
                    
                    struct timeval tv;
                    tv.tv_sec = timeout / 1000;
                    tv.tv_usec = (timeout % 1000) * 1000;
                    
                    if (select(0, NULL, &writeSet, NULL, &tv) > 0) {
                        int error = 0;
                        int len = sizeof(error);
                        if (getsockopt(sock, SOL_SOCKET, SO_ERROR, (char*)&error, &len) == 0 && error == 0) {
                            result = TRUE;
                        }
                    }
                }
            } else {
                result = TRUE;
            }
            
            closesocket(sock);
            WSACleanup();
            return result;
        }

        // ConnectionManager implementation
        ConnectionManager::ConnectionManager() : m_initialized(FALSE), m_connected(FALSE), m_connectedPort(0) {
            memset(&m_config, 0, sizeof(m_config));
            memset(m_connectedHost, 0, sizeof(m_connectedHost));
        }

        ConnectionManager::~ConnectionManager() {
            Cleanup();
        }

        BOOL ConnectionManager::Initialize(const ConnectionConfig* config) {
            if (!config) return FALSE;

            memcpy(&m_config, config, sizeof(ConnectionConfig));
            m_initialized = TRUE;
            return TRUE;
        }

        SOCKET ConnectionManager::ConnectToServer() {
            if (!m_initialized) return INVALID_SOCKET;

            SOCKET sock = INVALID_SOCKET;

            // Try direct connection first
            if (TryDirectConnection()) {
                sock = AttemptConnection(m_connectedHost, m_connectedPort);
                if (sock != INVALID_SOCKET) {
                    m_connected = TRUE;
                    return sock;
                }
            }

            // Try relay connection if enabled and direct failed
            if (m_config.enableRelay && strlen(m_config.relayHost) > 0) {
                if (TryRelayConnection()) {
                    sock = AttemptConnection(m_config.relayHost, m_config.relayPort);
                    if (sock != INVALID_SOCKET) {
                        m_connected = TRUE;
                        strcpy_s(m_connectedHost, sizeof(m_connectedHost), m_config.relayHost);
                        m_connectedPort = m_config.relayPort;
                        return sock;
                    }
                }
            }

            return INVALID_SOCKET;
        }

        BOOL ConnectionManager::TryDirectConnection() {
            // Try primary host first
            if (strlen(m_config.primaryHost) > 0) {
                for (DWORD attempt = 0; attempt < m_config.retryAttempts; attempt++) {
                    if (NetworkUtils::TestConnection(m_config.primaryHost, m_config.primaryPort, m_config.connectionTimeout)) {
                        strcpy_s(m_connectedHost, sizeof(m_connectedHost), m_config.primaryHost);
                        m_connectedPort = m_config.primaryPort;
                        return TRUE;
                    }
                    if (attempt < m_config.retryAttempts - 1) {
                        Sleep(m_config.retryDelay);
                    }
                }
            }

            // Try fallback host if primary failed
            if (strlen(m_config.fallbackHost) > 0) {
                for (DWORD attempt = 0; attempt < m_config.retryAttempts; attempt++) {
                    if (NetworkUtils::TestConnection(m_config.fallbackHost, m_config.fallbackPort, m_config.connectionTimeout)) {
                        strcpy_s(m_connectedHost, sizeof(m_connectedHost), m_config.fallbackHost);
                        m_connectedPort = m_config.fallbackPort;
                        return TRUE;
                    }
                    if (attempt < m_config.retryAttempts - 1) {
                        Sleep(m_config.retryDelay);
                    }
                }
            }

            return FALSE;
        }

        BOOL ConnectionManager::TryRelayConnection() {
            if (strlen(m_config.relayHost) == 0) return FALSE;

            return NetworkUtils::TestConnection(m_config.relayHost, m_config.relayPort, m_config.connectionTimeout);
        }

        SOCKET ConnectionManager::AttemptConnection(const char* host, int port) {
            WSADATA wsaData;
            if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) return INVALID_SOCKET;

            SOCKET sock = socket(AF_INET, SOCK_STREAM, 0);
            if (sock == INVALID_SOCKET) {
                WSACleanup();
                return INVALID_SOCKET;
            }

            struct sockaddr_in addr;
            addr.sin_family = AF_INET;
            addr.sin_port = htons(port);

            if (inet_pton(AF_INET, host, &addr.sin_addr) != 1) {
                struct hostent* he = gethostbyname(host);
                if (!he) {
                    closesocket(sock);
                    WSACleanup();
                    return INVALID_SOCKET;
                }
                memcpy(&addr.sin_addr, he->h_addr_list[0], he->h_length);
            }

            if (connect(sock, (struct sockaddr*)&addr, sizeof(addr)) == SOCKET_ERROR) {
                closesocket(sock);
                WSACleanup();
                return INVALID_SOCKET;
            }

            return sock;
        }

        void ConnectionManager::Cleanup() {
            m_initialized = FALSE;
            m_connected = FALSE;
            memset(m_connectedHost, 0, sizeof(m_connectedHost));
            m_connectedPort = 0;
        }

        // ArgumentParser implementation
        BOOL ArgumentParser::ParseNetworkArgs(int argc, char* argv[], ConnectionConfig* config) {
            if (!config) return FALSE;

            NetworkUtils::SetDefaultConfig(config);

            for (int i = 1; i < argc; i++) {
                if (strcmp(argv[i], "--host") == 0 || strcmp(argv[i], "-h") == 0) {
                    if (i + 1 < argc) {
                        strcpy_s(config->primaryHost, sizeof(config->primaryHost), argv[++i]);
                    }
                } else if (strcmp(argv[i], "--port") == 0 || strcmp(argv[i], "-p") == 0) {
                    if (i + 1 < argc) {
                        config->primaryPort = atoi(argv[++i]);
                    }
                } else if (strcmp(argv[i], "--fallback") == 0 || strcmp(argv[i], "-f") == 0) {
                    if (i + 1 < argc) {
                        ParseHostPort(argv[++i], config->fallbackHost, &config->fallbackPort);
                    }
                } else if (strcmp(argv[i], "--config") == 0 || strcmp(argv[i], "-c") == 0) {
                    if (i + 1 < argc) {
                        NetworkUtils::LoadNetworkConfig(argv[++i], config);
                    }
                } else if (strcmp(argv[i], "--external-ip") == 0) {
                    config->useExternalIP = TRUE;
                } else if (strcmp(argv[i], "--enable-upnp") == 0) {
                    config->enablePortForwarding = TRUE;
                } else if (strcmp(argv[i], "--timeout") == 0 || strcmp(argv[i], "-t") == 0) {
                    if (i + 1 < argc) {
                        config->connectionTimeout = atoi(argv[++i]);
                    }
                } else if (strcmp(argv[i], "--help") == 0) {
                    PrintUsage();
                    return FALSE;
                }
            }

            return TRUE;
        }

        BOOL ArgumentParser::ParseHostPort(const char* hostPort, char* host, int* port) {
            if (!hostPort || !host || !port) return FALSE;

            const char* colon = strchr(hostPort, ':');
            if (colon) {
                size_t hostLen = colon - hostPort;
                if (hostLen >= 256) return FALSE;

                strncpy_s(host, 256, hostPort, hostLen);
                host[hostLen] = '\0';
                *port = atoi(colon + 1);
            } else {
                strcpy_s(host, 256, hostPort);
                *port = 4043; // Default port
            }

            return TRUE;
        }

        void ArgumentParser::PrintUsage() {
            printf("HVNC Client - Network Configuration Options:\n\n");
            printf("Usage: Client.exe [options]\n\n");
            printf("Options:\n");
            printf("  -h, --host <host>        Primary server host/IP (default: 127.0.0.1)\n");
            printf("  -p, --port <port>        Primary server port (default: 4043)\n");
            printf("  -f, --fallback <host:port> Fallback server (format: host:port)\n");
            printf("  -c, --config <file>      Load configuration from file\n");
            printf("  -t, --timeout <ms>       Connection timeout in milliseconds\n");
            printf("      --external-ip        Try to detect external IP address\n");
            printf("      --enable-upnp        Enable UPnP port forwarding\n");
            printf("      --help               Show this help message\n\n");
            printf("Examples:\n");
            printf("  Client.exe --host ************* --port 8080\n");
            printf("  Client.exe --config network.ini\n");
            printf("  Client.exe --host server.example.com --fallback *************:4043\n");
        }

    } // namespace Network
} // namespace ModernHVNC
