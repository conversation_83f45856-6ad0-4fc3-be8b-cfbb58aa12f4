# HVNC Network Configuration Examples
# Copy the appropriate section to hvnc_network.ini and modify as needed

# ============================================================================
# EXAMPLE 1: LOCAL NETWORK (Same WiFi/LAN)
# Use this when client and server are on the same local network
# ============================================================================
[Network_Local]
primary_host=*************    # Server's local IP address
primary_port=4043             # Default HVNC port
fallback_host=                # No fallback needed for local
fallback_port=8080
use_external_ip=false         # Don't need external IP for local
enable_port_forwarding=false  # No port forwarding needed
connection_timeout=5000       # 5 second timeout (local is fast)
retry_attempts=3              # 3 retry attempts
retry_delay=1000              # 1 second between retries
enable_relay=false            # No relay needed for local
relay_host=
relay_port=9090

# ============================================================================
# EXAMPLE 2: REMOTE NETWORK (Internet Connection)
# Use this when client and server are on different networks/internet
# Requires port forwarding on server's router
# ============================================================================
[Network_Remote]
primary_host=************     # Server's external/public IP
primary_port=4043             # External port (forwarded to server)
fallback_host=                # Optional: backup server
fallback_port=8080
use_external_ip=false         # We already know the external IP
enable_port_forwarding=false  # Manual port forwarding configured
connection_timeout=10000      # 10 second timeout (internet can be slow)
retry_attempts=5              # More retries for internet connections
retry_delay=2000              # 2 seconds between retries
enable_relay=false            # Direct connection
relay_host=
relay_port=9090

# ============================================================================
# EXAMPLE 3: DYNAMIC DNS SETUP
# Use this when server's IP changes frequently
# Requires dynamic DNS service (No-IP, DynDNS, etc.)
# ============================================================================
[Network_DynamicDNS]
primary_host=myhvnc.ddns.net  # Dynamic DNS hostname
primary_port=4043
fallback_host=backup.ddns.net # Backup dynamic DNS
fallback_port=4043
use_external_ip=false         # Using hostname instead of IP
enable_port_forwarding=false
connection_timeout=8000       # DNS resolution may take time
retry_attempts=4
retry_delay=1500
enable_relay=false
relay_host=
relay_port=9090

# ============================================================================
# EXAMPLE 4: CORPORATE/OFFICE NETWORK
# Use this for office environments with specific network policies
# May require IT department assistance for firewall/proxy configuration
# ============================================================================
[Network_Corporate]
primary_host=**********       # Corporate network IP
primary_port=8080             # Non-standard port (may bypass some filters)
fallback_host=**********      # Backup server in different subnet
fallback_port=8443            # HTTPS port (often allowed)
use_external_ip=false
enable_port_forwarding=false
connection_timeout=15000      # Corporate networks can be slow
retry_attempts=3
retry_delay=3000              # Longer delay for corporate policies
enable_relay=false
relay_host=
relay_port=9090

# ============================================================================
# EXAMPLE 5: RELAY SERVER SETUP
# Use this when direct connections fail due to NAT/firewall issues
# Requires a relay server deployed on public cloud (AWS, Azure, etc.)
# ============================================================================
[Network_Relay]
primary_host=*************    # Try direct connection first
primary_port=4043
fallback_host=                # No fallback, use relay instead
fallback_port=8080
use_external_ip=false
enable_port_forwarding=false
connection_timeout=8000
retry_attempts=2              # Fewer retries before switching to relay
retry_delay=1000
enable_relay=true             # Enable relay fallback
relay_host=relay.example.com  # Public relay server
relay_port=9090               # Relay server port

# ============================================================================
# EXAMPLE 6: HIGH AVAILABILITY SETUP
# Use this for mission-critical connections with multiple fallbacks
# ============================================================================
[Network_HighAvailability]
primary_host=server1.example.com
primary_port=4043
fallback_host=server2.example.com  # Geographic backup
fallback_port=4043
use_external_ip=false
enable_port_forwarding=false
connection_timeout=6000
retry_attempts=2              # Quick failover
retry_delay=500               # Fast retry
enable_relay=true             # Ultimate fallback
relay_host=relay.example.com
relay_port=9090

# ============================================================================
# EXAMPLE 7: MOBILE/CELLULAR CONNECTION
# Use this when connecting from mobile hotspot or cellular data
# Optimized for higher latency and potential connection instability
# ============================================================================
[Network_Mobile]
primary_host=************     # Server's external IP
primary_port=4043
fallback_host=                # Mobile connections are often unstable
fallback_port=8080
use_external_ip=false
enable_port_forwarding=false
connection_timeout=20000      # Very long timeout for cellular
retry_attempts=8              # Many retries for unstable connections
retry_delay=3000              # Long delay between retries
enable_relay=false
relay_host=
relay_port=9090

# ============================================================================
# EXAMPLE 8: DEVELOPMENT/TESTING SETUP
# Use this for development and testing scenarios
# Includes extensive logging and debugging options
# ============================================================================
[Network_Development]
primary_host=127.0.0.1        # Localhost for testing
primary_port=4043
fallback_host=*************   # Local network fallback
fallback_port=4043
use_external_ip=false
enable_port_forwarding=false
connection_timeout=3000       # Short timeout for quick testing
retry_attempts=1              # Single attempt for faster debugging
retry_delay=500
enable_relay=false
relay_host=
relay_port=9090

# ============================================================================
# EXAMPLE 9: VPN CONNECTION
# Use this when connecting through VPN
# VPN provides secure tunnel, so local IPs can be used
# ============================================================================
[Network_VPN]
primary_host=**********       # VPN network IP (OpenVPN default range)
primary_port=4043
fallback_host=**********      # Backup VPN server
fallback_port=4043
use_external_ip=false         # VPN handles routing
enable_port_forwarding=false  # VPN handles NAT traversal
connection_timeout=7000       # VPN may add latency
retry_attempts=3
retry_delay=1500
enable_relay=false            # VPN replaces need for relay
relay_host=
relay_port=9090

# ============================================================================
# EXAMPLE 10: CLOUD DEPLOYMENT
# Use this when both client and server are in cloud environments
# (AWS, Azure, Google Cloud, etc.)
# ============================================================================
[Network_Cloud]
primary_host=ec2-203-0-113-45.compute-1.amazonaws.com  # AWS instance
primary_port=4043
fallback_host=vm-backup.cloudapp.azure.com             # Azure backup
fallback_port=4043
use_external_ip=false         # Using cloud DNS names
enable_port_forwarding=false  # Cloud security groups handle this
connection_timeout=5000       # Cloud networks are usually fast
retry_attempts=3
retry_delay=1000
enable_relay=false
relay_host=
relay_port=9090

# ============================================================================
# CONFIGURATION NOTES:
# 
# 1. Copy the appropriate [Network_*] section to hvnc_network.ini
# 2. Rename the section to [Network]
# 3. Modify IP addresses, hostnames, and ports as needed
# 4. Test the configuration using NetworkConfigTool.exe
# 5. Ensure firewall and port forwarding are configured correctly
#
# SECURITY RECOMMENDATIONS:
# - Use VPN for sensitive connections
# - Change default ports to reduce automated attacks
# - Monitor logs for unauthorized access attempts
# - Use strong authentication if implemented
# - Limit port forwarding to specific IP ranges when possible
# ============================================================================
