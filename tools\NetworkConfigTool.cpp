#include "../common/NetworkConfig.h"
#include <iostream>
#include <string>

using namespace ModernHVNC::Network;

void PrintNetworkInfo() {
    std::cout << "\n=== Network Information ===" << std::endl;
    
    // Get local IP
    char localIP[16];
    if (NetworkUtils::GetLocalIP(localIP, sizeof(localIP))) {
        std::cout << "Local IP Address: " << localIP << std::endl;
        std::cout << "Is Private IP: " << (NetworkUtils::IsPrivateIP(localIP) ? "Yes" : "No") << std::endl;
    } else {
        std::cout << "Failed to get local IP address" << std::endl;
    }
    
    // Get external IP
    char externalIP[16];
    if (NetworkUtils::GetExternalIP(externalIP, sizeof(externalIP))) {
        std::cout << "External IP Address: " << externalIP << std::endl;
    } else {
        std::cout << "Failed to get external IP address (check internet connection)" << std::endl;
    }
    
    // Check internet connectivity
    std::cout << "Internet Connected: " << (NetworkUtils::IsConnectedToInternet() ? "Yes" : "No") << std::endl;
    
    std::cout << "==============================\n" << std::endl;
}

void TestConnection() {
    std::string host;
    int port;
    
    std::cout << "Enter host/IP to test: ";
    std::getline(std::cin, host);
    
    std::cout << "Enter port: ";
    std::cin >> port;
    std::cin.ignore();
    
    std::cout << "Testing connection to " << host << ":" << port << "..." << std::endl;
    
    if (NetworkUtils::TestConnection(host.c_str(), port, 5000)) {
        std::cout << "✓ Connection successful!" << std::endl;
    } else {
        std::cout << "✗ Connection failed!" << std::endl;
    }
}

void CreateNetworkConfig() {
    ConnectionConfig config;
    NetworkUtils::SetDefaultConfig(&config);
    
    std::string input;
    
    std::cout << "\n=== Create Network Configuration ===" << std::endl;
    
    std::cout << "Primary host/IP [" << config.primaryHost << "]: ";
    std::getline(std::cin, input);
    if (!input.empty()) {
        strcpy_s(config.primaryHost, sizeof(config.primaryHost), input.c_str());
    }
    
    std::cout << "Primary port [" << config.primaryPort << "]: ";
    std::getline(std::cin, input);
    if (!input.empty()) {
        config.primaryPort = std::stoi(input);
    }
    
    std::cout << "Fallback host/IP (optional): ";
    std::getline(std::cin, input);
    if (!input.empty()) {
        strcpy_s(config.fallbackHost, sizeof(config.fallbackHost), input.c_str());
    }
    
    std::cout << "Fallback port [" << config.fallbackPort << "]: ";
    std::getline(std::cin, input);
    if (!input.empty()) {
        config.fallbackPort = std::stoi(input);
    }
    
    std::cout << "Use external IP detection? (y/n) [n]: ";
    std::getline(std::cin, input);
    config.useExternalIP = (input == "y" || input == "Y");
    
    std::cout << "Connection timeout in ms [" << config.connectionTimeout << "]: ";
    std::getline(std::cin, input);
    if (!input.empty()) {
        config.connectionTimeout = std::stoul(input);
    }
    
    std::cout << "Retry attempts [" << config.retryAttempts << "]: ";
    std::getline(std::cin, input);
    if (!input.empty()) {
        config.retryAttempts = std::stoul(input);
    }
    
    // Save configuration
    std::string filename = "hvnc_network.ini";
    std::cout << "Save as [" << filename << "]: ";
    std::getline(std::cin, input);
    if (!input.empty()) {
        filename = input;
    }
    
    if (NetworkUtils::SaveNetworkConfig(filename.c_str(), &config)) {
        std::cout << "✓ Configuration saved to " << filename << std::endl;
    } else {
        std::cout << "✗ Failed to save configuration" << std::endl;
    }
}

void ShowCurrentConfig() {
    ConnectionConfig config;
    
    if (NetworkUtils::LoadNetworkConfig("hvnc_network.ini", &config)) {
        std::cout << "\n=== Current Configuration ===" << std::endl;
        std::cout << "Primary Host: " << config.primaryHost << ":" << config.primaryPort << std::endl;
        std::cout << "Fallback Host: " << (strlen(config.fallbackHost) > 0 ? config.fallbackHost : "None") 
                  << (strlen(config.fallbackHost) > 0 ? (":" + std::to_string(config.fallbackPort)) : "") << std::endl;
        std::cout << "Use External IP: " << (config.useExternalIP ? "Yes" : "No") << std::endl;
        std::cout << "Port Forwarding: " << (config.enablePortForwarding ? "Enabled" : "Disabled") << std::endl;
        std::cout << "Connection Timeout: " << config.connectionTimeout << "ms" << std::endl;
        std::cout << "Retry Attempts: " << config.retryAttempts << std::endl;
        std::cout << "Retry Delay: " << config.retryDelay << "ms" << std::endl;
        std::cout << "Relay Enabled: " << (config.enableRelay ? "Yes" : "No") << std::endl;
        if (config.enableRelay && strlen(config.relayHost) > 0) {
            std::cout << "Relay Host: " << config.relayHost << ":" << config.relayPort << std::endl;
        }
        std::cout << "==============================\n" << std::endl;
    } else {
        std::cout << "No configuration file found or failed to load." << std::endl;
    }
}

void ShowHelp() {
    std::cout << "\n=== HVNC Network Configuration Tool ===" << std::endl;
    std::cout << "This tool helps configure HVNC for remote connections across different networks.\n" << std::endl;
    
    std::cout << "Menu Options:" << std::endl;
    std::cout << "1. Show Network Information - Display local/external IP and connectivity status" << std::endl;
    std::cout << "2. Test Connection - Test connectivity to a specific host and port" << std::endl;
    std::cout << "3. Create Configuration - Create a new network configuration file" << std::endl;
    std::cout << "4. Show Current Config - Display current configuration settings" << std::endl;
    std::cout << "5. Help - Show this help information" << std::endl;
    std::cout << "6. Exit - Exit the tool\n" << std::endl;
    
    std::cout << "For remote connections across different networks:" << std::endl;
    std::cout << "• Set the primary host to the server's external/public IP address" << std::endl;
    std::cout << "• Configure port forwarding on the server's router" << std::endl;
    std::cout << "• Ensure Windows Firewall allows the connection" << std::endl;
    std::cout << "• Consider using a VPN for enhanced security" << std::endl;
    std::cout << "========================================\n" << std::endl;
}

int main() {
    std::cout << "HVNC Network Configuration Tool v1.0" << std::endl;
    std::cout << "=====================================" << std::endl;
    
    int choice;
    std::string input;
    
    while (true) {
        std::cout << "\nMenu:" << std::endl;
        std::cout << "1. Show Network Information" << std::endl;
        std::cout << "2. Test Connection" << std::endl;
        std::cout << "3. Create Configuration" << std::endl;
        std::cout << "4. Show Current Config" << std::endl;
        std::cout << "5. Help" << std::endl;
        std::cout << "6. Exit" << std::endl;
        std::cout << "\nEnter choice (1-6): ";
        
        std::getline(std::cin, input);
        
        try {
            choice = std::stoi(input);
        } catch (...) {
            choice = 0;
        }
        
        switch (choice) {
            case 1:
                PrintNetworkInfo();
                break;
            case 2:
                TestConnection();
                break;
            case 3:
                CreateNetworkConfig();
                break;
            case 4:
                ShowCurrentConfig();
                break;
            case 5:
                ShowHelp();
                break;
            case 6:
                std::cout << "Goodbye!" << std::endl;
                return 0;
            default:
                std::cout << "Invalid choice. Please enter 1-6." << std::endl;
                break;
        }
    }
    
    return 0;
}
