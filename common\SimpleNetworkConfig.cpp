#include "SimpleNetworkConfig.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

void SetDefaultSimpleNetworkConfig(SimpleNetworkConfig* config) {
    if (!config) return;
    
    strcpy_s(config->primaryHost, sizeof(config->primaryHost), "127.0.0.1");
    config->primaryPort = 4043;
    strcpy_s(config->fallbackHost, sizeof(config->fallbackHost), "");
    config->fallbackPort = 8080;
    config->connectionTimeout = 5000;
    config->retryAttempts = 3;
    config->retryDelay = 1000;
}

BOOL LoadSimpleNetworkConfig(const char* configFile, SimpleNetworkConfig* config) {
    if (!configFile || !config) return FALSE;
    
    FILE* file = nullptr;
    if (fopen_s(&file, configFile, "r") != 0 || !file) {
        // If config file doesn't exist, create default one
        SetDefaultSimpleNetworkConfig(config);
        SaveSimpleNetworkConfig(configFile, config);
        return TRUE;
    }
    
    SetDefaultSimpleNetworkConfig(config); // Start with defaults
    
    char line[512];
    while (fgets(line, sizeof(line), file)) {
        // Remove newline
        line[strcspn(line, "\r\n")] = 0;
        
        // Skip comments and empty lines
        if (line[0] == '#' || line[0] == ';' || line[0] == '\0') continue;
        if (strstr(line, "[Network]")) continue;
        
        char* equals = strchr(line, '=');
        if (!equals) continue;
        
        *equals = '\0';
        char* key = line;
        char* value = equals + 1;
        
        // Trim whitespace
        while (*key == ' ' || *key == '\t') key++;
        while (*value == ' ' || *value == '\t') value++;
        
        if (strcmp(key, "primary_host") == 0) {
            strcpy_s(config->primaryHost, sizeof(config->primaryHost), value);
        } else if (strcmp(key, "primary_port") == 0) {
            config->primaryPort = atoi(value);
        } else if (strcmp(key, "fallback_host") == 0) {
            strcpy_s(config->fallbackHost, sizeof(config->fallbackHost), value);
        } else if (strcmp(key, "fallback_port") == 0) {
            config->fallbackPort = atoi(value);
        } else if (strcmp(key, "connection_timeout") == 0) {
            config->connectionTimeout = atoi(value);
        } else if (strcmp(key, "retry_attempts") == 0) {
            config->retryAttempts = atoi(value);
        } else if (strcmp(key, "retry_delay") == 0) {
            config->retryDelay = atoi(value);
        }
    }
    
    fclose(file);
    return TRUE;
}

BOOL SaveSimpleNetworkConfig(const char* configFile, const SimpleNetworkConfig* config) {
    if (!configFile || !config) return FALSE;
    
    FILE* file = nullptr;
    if (fopen_s(&file, configFile, "w") != 0 || !file) return FALSE;
    
    fprintf(file, "# HVNC Simple Network Configuration\n");
    fprintf(file, "# This file configures basic network connection settings\n\n");
    fprintf(file, "[Network]\n");
    fprintf(file, "primary_host=%s\n", config->primaryHost);
    fprintf(file, "primary_port=%d\n", config->primaryPort);
    fprintf(file, "fallback_host=%s\n", config->fallbackHost);
    fprintf(file, "fallback_port=%d\n", config->fallbackPort);
    fprintf(file, "connection_timeout=%lu\n", config->connectionTimeout);
    fprintf(file, "retry_attempts=%lu\n", config->retryAttempts);
    fprintf(file, "retry_delay=%lu\n", config->retryDelay);
    
    fclose(file);
    return TRUE;
}

BOOL TestSimpleConnection(const char* host, int port, DWORD timeout) {
    // This is a placeholder - actual implementation would require WinSock
    // For now, just return TRUE to avoid build conflicts
    return TRUE;
}
