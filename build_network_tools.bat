@echo off
setlocal enabledelayedexpansion

title HVNC Network Tools Builder
color 0B

echo ===============================================
echo    HVNC Network Tools Builder v1.0
echo ===============================================
echo.

:: Check if Visual Studio tools are available
where cl >nul 2>&1
if %errorlevel% neq 0 (
    echo Setting up Visual Studio environment...
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
    if %errorlevel% neq 0 (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
        if %errorlevel% neq 0 (
            echo Error: Visual Studio not found or not properly installed.
            echo Please install Visual Studio with C++ development tools.
            pause
            exit /b 1
        )
    )
)

:: Create tools build directory
if not exist "tools\build" mkdir "tools\build"

echo Building Network Configuration Tool...
echo =====================================

:: Compile NetworkConfig.cpp
cl /c /EHsc /I. common\NetworkConfig.cpp /Fo:tools\build\NetworkConfig.obj
if %errorlevel% neq 0 (
    echo Error: Failed to compile NetworkConfig.cpp
    goto error
)

:: Compile NetworkConfigTool.cpp
cl /c /EHsc /I. tools\NetworkConfigTool.cpp /Fo:tools\build\NetworkConfigTool.obj
if %errorlevel% neq 0 (
    echo Error: Failed to compile NetworkConfigTool.cpp
    goto error
)

:: Link NetworkConfigTool
cl /Fe:tools\build\NetworkConfigTool.exe tools\build\NetworkConfigTool.obj tools\build\NetworkConfig.obj ws2_32.lib wininet.lib
if %errorlevel% neq 0 (
    echo Error: Failed to link NetworkConfigTool.exe
    goto error
)

echo ✓ NetworkConfigTool.exe built successfully!
echo.

echo Building Relay Server...
echo =======================

:: Compile RelayServer.cpp
cl /EHsc /Fe:tools\build\RelayServer.exe tools\RelayServer.cpp ws2_32.lib
if %errorlevel% neq 0 (
    echo Error: Failed to compile RelayServer.cpp
    goto error
)

echo ✓ RelayServer.exe built successfully!
echo.

echo Building Enhanced Client and Server...
echo =====================================

:: Build enhanced client
echo Building enhanced client...
cl /c /EHsc /I. common\NetworkConfig.cpp /Fo:Client\build\NetworkConfig.obj
if %errorlevel% neq 0 (
    echo Warning: Failed to compile NetworkConfig.cpp for client
)

:: Note: The main client and server builds would need to be integrated with the existing build system
echo Note: Enhanced client and server require integration with existing build system.
echo Please use the main build.bat script after these tools are built.
echo.

echo Copying configuration files...
echo =============================

:: Copy network configuration files to build directory
copy "hvnc_network.ini" "tools\build\" >nul 2>&1
copy "configure_network.bat" "tools\build\" >nul 2>&1

echo ✓ Configuration files copied!
echo.

echo ===============================================
echo           BUILD COMPLETED SUCCESSFULLY!
echo ===============================================
echo.
echo Built tools:
echo • tools\build\NetworkConfigTool.exe - Network configuration utility
echo • tools\build\RelayServer.exe - Relay server for NAT traversal
echo • tools\build\hvnc_network.ini - Default network configuration
echo • tools\build\configure_network.bat - Interactive setup script
echo.
echo Usage:
echo 1. Run NetworkConfigTool.exe to configure network settings
echo 2. Use configure_network.bat for guided setup
echo 3. Deploy RelayServer.exe on a public server for complex scenarios
echo.
echo Next steps:
echo 1. Configure your network settings using the tools
echo 2. Build the main HVNC project with build.bat
echo 3. Test connections using the network configuration tool
echo.
pause
goto end

:error
echo.
echo ===============================================
echo              BUILD FAILED!
echo ===============================================
echo.
echo Please check the error messages above and ensure:
echo • Visual Studio with C++ tools is installed
echo • All source files are present
echo • No antivirus interference
echo.
pause
exit /b 1

:end
echo Build process completed.
exit /b 0
