#pragma once
#include <Windows.h>

// Simple network configuration structure
struct SimpleNetworkConfig {
    char primaryHost[256];
    int primaryPort;
    char fallbackHost[256];
    int fallbackPort;
    DWORD connectionTimeout;
    DWORD retryAttempts;
    DWORD retryDelay;
};

// Simple network configuration functions
BOOL LoadSimpleNetworkConfig(const char* configFile, SimpleNetworkConfig* config);
BOOL SaveSimpleNetworkConfig(const char* configFile, const SimpleNetworkConfig* config);
void SetDefaultSimpleNetworkConfig(SimpleNetworkConfig* config);
BOOL TestSimpleConnection(const char* host, int port, DWORD timeout);
